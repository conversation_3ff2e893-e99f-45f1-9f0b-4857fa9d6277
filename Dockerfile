FROM registry.duopu.cn/zyws/docker/node:18-alpine

LABEL author="<EMAIL>"

ENV PORT=3000 NODE_ENV=production

WORKDIR /app
COPY package.json README.md version /app/
COPY ./dist/ dist/

# RUN npm config set proxy http://192.168.0.3:7890
# RUN npm config set https-proxy http://192.168.0.3:7890
RUN npm config set registry https://registry.npmmirror.com
RUN npm install -g @nestjs/cli
RUN npm install

EXPOSE ${PORT}

ENTRYPOINT [ "npm", "run" ]
CMD [ "start:prod" ]
