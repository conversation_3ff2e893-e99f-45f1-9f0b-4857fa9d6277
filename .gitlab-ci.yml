stages:
  - build
  - test
  - release

build site:
  image: node:18-alpine
  stage: build
  tags:
    - nodejs-dock
  cache:
    key: GWBKNPM
    paths:
      - .npm/
      - node_modules/
  before_script:
    - npm config set registry https://registry.npmmirror.com
    - npm i --cache .npm --include=dev --prefer-offline
  script:
    - ls $CI_PROJECT_DIR
    # - npm run lint
    - npm run build
  artifacts:
    expire_in: 1 week
    paths:
      - dist
      - public
  except:
    - tags

unit test:
  image: node:18-alpine
  stage: test
  tags:
    - nodejs-dock
  cache:
    key: GWBKNPM
    paths:
      - .npm/
      - node_modules/
  before_script:
    - npm config set registry https://registry.npmmirror.com
    - npm i --cache .npm --include=dev --prefer-offline
  script:
    - ls $CI_PROJECT_DIR
    # - npm run test
    # - npm run test:e2e
    - npm run test:cov
    # store and publish code coverage HTML report folder
    # https://about.gitlab.com/blog/2016/11/03/publish-code-coverage-report-with-gitlab-pages/
    # the coverage report will be available both as a job artifact
    # and at https://gitlab.duopu.cn/jkqy/zyjk/
    - ls
    - mkdir -p public/$CI_COMMIT_REF_NAME
    - cp -r coverage/lcov-report/* public/$CI_COMMIT_REF_NAME
  artifacts:
    when: always
    paths:
      # save the coverage results
      - coverage
      - public
    expire_in: 30 days
  except:
    - tags

release-image:
  stage: release
  image: docker:26.1
  tags:
    - nodejs-dock
  services:
    - docker:26.1.4-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:latest
  script:
    - ls -l .
    - source ./version
    - docker login -u$CI_TA_ALL -p $CI_TA_ALL_PASS $CI_REGISTRY
    - docker run --rm --privileged  --network zyws-net multiarch/qemu-user-static --reset -p yes
    - docker buildx build --platform=linux/amd64,linux/arm64 -t $CI_REGISTRY_IMAGE:$VERSION -t $CONTAINER_RELEASE_IMAGE --push .
    - docker logout
    - docker rmi -f $CI_REGISTRY_IMAGE:$VERSION $CONTAINER_RELEASE_IMAGE
    - docker images -f dangling=true -q | xargs --no-run-if-empty docker rmi
    - docker images
    - rm -rf ./dist/

  only:
    - main
    - ep_login
    - wb_record
  except:
    - tags
