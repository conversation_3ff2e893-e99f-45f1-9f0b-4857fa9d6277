export default () => ({
  database_mysql: {
    type: 'mysql',
    host: process.env.mysqlHost || 'zyws.cn',
    port: +process.env.mysqlPort || 30366,
    username: process.env.mysqlUser || 'root',
    password: process.env.mysqlPass,
    database: process.env.mysqlDb || 'ng_dak',
    logging: 'info',
    synchronize: false,
    timezone: '-08:00',
  },

  database_mongodb: {
    type: 'mongodb',
    host: process.env.mdbHost || 'mdb.duopu.cn',
    port: +process.env.mdbPort || 25000,
    username: process.env.mdbUser || 'dbadmin',
    password: process.env.mdbPass || 'DpPass6789',
    database: process.env.mdbName || 'zyws-xjbt0108',
    authSource: 'admin',
    logging: 'info',
    hostReplicaSet: process.env.mdbHostRs,
    replicaSet: process.env.mdbRs,
    readPreference: 'nearest',
    ssl: false,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    synchronize: false,
    autoLoadEntities: false,
  },

});
