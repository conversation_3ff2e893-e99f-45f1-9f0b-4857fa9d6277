export default () => ({
  port: 3001,
  // JWT
  jwtModule: {
    secrect: 'zwy667788',
    expiresIn: '3d',
  },

  // 本地上传文件路径
  uploadPath: './public/uploads',
  uploadPathUrl: '/uploads',

  // 百度地图接口配置
  bmap: {
    url: 'https://api.map.baidu.com/geocoding/v3/',
    ak: 'k14GSWM76SjqfMZcdOWacMb3XmV9sdGl',
    styleId: 'bf656ab5c64b329624efdd27a48e953b',
  },

  iServiceHost: process.env.iServicev2Host || 'http://iservicev2',

  sso: {
    httpUrl: process.env.sso_http_url || 'https://xjbtportal.jkqy.cn', // sso服务地址
    clientId: process.env.sso_client_id || '3mftbBx7lWHL0wPwFPRW7', // 客户端注册id
    clientSecret: process.env.sso_client_secret || 'HXGstBb8FJBt0Jh1EuoeK', // 客户端注册密码
    redirect_uri: process.env.sso_service_url || 'https://xjbtdak.jkqy.cn', // 获取code后重定向的地址
    response_type: 'code', // 授权类型，必选项，此处的值固定为“code”
    scope: 'read', // 申请的权限范围，可选项
  },
});
