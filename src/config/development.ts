export default () => ({
  database_mysql: {
    // name: 'mysqlConnection',
    type: 'mysql',
    host: '127.0.0.1',
    port: 3306,
    username: 'root',
    password: '123456',
    database: 'archive',
    // entities: [__dirname + '/**/*.entity{.ts,.js}'],
    logging: true, // 日志记录的级别
    autoLoadEntities: false, // 是否自动加载实体。默认值为false。如果设置为true，则在应用程序初始化期间自动加载所有实体。
    synchronize: true, // 是否在每次应用程序启动时自动同步数据库模式。这将根据实体的更改自动创建数据库表。
  },

  database_mongodb: {
    // name: 'mongodbConnection',
    type: 'mongodb',
    host: 'mdb.duopu.cn',
    port: 25000,
    database: 'zyws-xjbt0612',
    username: 'dbadmin',
    password: 'DpPass6789',
    authSource: 'admin',
    // entities: [__dirname + '/**/mongo/*.entity{.ts,.js}'],
    logging: true,
    // synchronize: true, // 生产环境不要使用
    useNewUrlParser: true,
    useUnifiedTopology: true,
  },

});
