import { Module } from '@nestjs/common';
import { SystemService } from './system/system.service';
import { OperationService } from './operation/operation.service';
import { ScheduleModule } from '@nestjs/schedule';

// 系统日志和操作日志模块
@Module({
  imports: [ScheduleModule.forRoot()],
  providers: [SystemService, OperationService],
  exports: [SystemService, OperationService],
})
export class LoggerModule {}
