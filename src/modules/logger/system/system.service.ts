// 系统日志服务
import { Injectable } from '@nestjs/common';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import { Cron } from '@nestjs/schedule';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class SystemService {
  private logger: winston.Logger;
  // 在 winston 中，日志级别的顺序是：error、warn、info、http、verbose、debug、silly
  constructor() {
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.timestamp(),
        // winston.format.json(),
        winston.format.printf(({ level, message, timestamp }) => {
          return `[Nest] ${
            process.pid
          }  - ${timestamp}  ${level.toUpperCase()} ${message}`;
        }),
      ),
      defaultMeta: { service: 'dak-service' },
      transports: [
        new DailyRotateFile({
          filename: 'logs/error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'error',
        }),
        new DailyRotateFile({
          filename: 'logs/combined-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          // level: 'warn',
        }),
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
          ),
        }),
      ],
    });
    // 如果不是生产环境，还需要在控制台打印日志
    // if (process.env.NODE_ENV !== 'production') {
    // this.logger.add(
    //   new winston.transports.Console({
    //     format: winston.format.combine(
    //       winston.format.colorize(),
    //       winston.format.simple(),
    //     ),
    //   }),
    // );
    // }
  }

  // 每天 0 点删除 7 天前的日志文件
  @Cron('0 0 * * *')
  async deleteOldLogs() {
    try {
      const logsDir = path.join(process.cwd(), 'logs');
      const files = await fs.promises.readdir(logsDir);
      const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;

      for (const file of files) {
        const filePath = path.join(logsDir, file);
        const stats = await fs.promises.stat(filePath);

        if (stats.isFile() && stats.birthtimeMs < sevenDaysAgo) {
          await fs.promises.unlink(filePath);
        }
      }

      // 删除public/temporary文件夹下的文件（临时文件）
      const temporaryDir = path.join(process.cwd(), 'public/temporary');
      if (fs.existsSync(temporaryDir)) {
        const temporaryFiles = await fs.promises.readdir(temporaryDir);
        for (const file of temporaryFiles) {
          const filePath = path.join(temporaryDir, file);
          const stats = await fs.promises.stat(filePath);
          if (stats.isFile()) {
            await fs.promises.unlink(filePath);
          }
        }
      }
    } catch (error) {
      this.logger.error(JSON.stringify(error));
    }
  }

  debug(message: string) {
    this.logger.debug(message);
  }

  verbose(message: string) {
    this.logger.verbose(message);
  }

  log(message: string) {
    this.logger.info(message);
  }

  warn(message: string) {
    this.logger.warn(message);
  }

  error(message: string) {
    this.logger.error(message);
  }
}
