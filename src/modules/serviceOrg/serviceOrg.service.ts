import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { ServiceOrg } from './mongo/serviceOrg.entity';
import * as moment from 'moment';

@Injectable()
export class ServiceOrgService {
  constructor(
    @InjectRepository(ServiceOrg, 'mongodbConnection')
    private readonly serviceOrgRepository: MongoRepository<ServiceOrg>
  ) { }

  /**
   * 创建服务机构
   * @param data 服务机构数据
   * @returns 创建的服务机构
   */
  async create(data: any): Promise<ServiceOrg> {
    // 检查是否已存在相同社会信用代码的机构
    const existingOrg = await this.serviceOrgRepository.findOne({ where: { code: data.code } });
    if (existingOrg) {
      throw new HttpException('已存在相同社会信用代码的机构', HttpStatus.BAD_REQUEST);
    }

    const serviceOrg = new ServiceOrg();

    // 基础信息
    serviceOrg.cname = data.cname;
    serviceOrg.area = data.area;
    serviceOrg.code = data.code;
    serviceOrg.industry = data.industry;
    serviceOrg.legalPerson = data.legalPerson;
    serviceOrg.legalPersonId = data.legalPersonId;
    serviceOrg.legalPersonTitle = data.legalPersonTitle;
    serviceOrg.zipCode = data.zipCode;
    serviceOrg.fax = data.fax;
    serviceOrg.email = data.email;
    serviceOrg.state = data.state !== undefined ? data.state : true;
    serviceOrg.remark = data.remark;

    // 机构类型
    serviceOrg.orgTypes = [];

    // 处理机构类型数组
    if (data.orgTypes && Array.isArray(data.orgTypes)) {
      serviceOrg.orgTypes = data.orgTypes;
    } else {
      // 兼容旧的布尔类型字段
      if (data.type1) serviceOrg.orgTypes.push(1);
      if (data.type2) serviceOrg.orgTypes.push(2);
      if (data.type3) serviceOrg.orgTypes.push(3);
      if (data.type4) serviceOrg.orgTypes.push(4);
      if (data.type5) serviceOrg.orgTypes.push(5);
      if (data.type6) serviceOrg.orgTypes.push(6);
      if (data.type7) serviceOrg.orgTypes.push(7);
    }

    // 医疗机构
    if (data.medicalInstitution) {
      serviceOrg.medicalInstitution = data.medicalInstitution;
    }

    // 职业卫生技术服务机构
    if (data.occupationalHealthTechnicalServiceInstitution) {
      serviceOrg.occupationalHealthTechnicalServiceInstitution = data.occupationalHealthTechnicalServiceInstitution;
    }

    // 职业病健康检查机构
    if (data.diseaseHealthCheckInstitution) {
      serviceOrg.diseaseHealthCheckInstitution = data.diseaseHealthCheckInstitution;
    }

    // 职业病诊断机构
    if (data.occupationalDiseaseDiagnosisInstitution) {
      serviceOrg.occupationalDiseaseDiagnosisInstitution = data.occupationalDiseaseDiagnosisInstitution;
    }

    // 放射性卫生服务机构
    if (data.radioHealthServiceInstitution) {
      serviceOrg.radioHealthServiceInstitution = data.radioHealthServiceInstitution;
    }

    // 联系人
    if (data.contacts && Array.isArray(data.contacts)) {
      serviceOrg.contacts = data.contacts;
    }

    return await this.serviceOrgRepository.save(serviceOrg);
  }

  /**
   * 获取服务机构列表
   * @param query 查询参数
   * @returns 服务机构列表和分页信息
   */
  async findAll(query: any): Promise<{ list: ServiceOrg[], total: number }> {
    const {
      cname,
      code,
      page = 1,
      pageSize = 10,
      state,
      orgType, // 新的机构类型查询参数
      type1,
      type2,
      type3,
      type4,
      type5,
      type6,
      type7
    } = query;

    const skip = (page - 1) * pageSize;
    const take = +pageSize;

    // 构建查询条件
    const where: any = {};

    if (cname) {
      where.cname = { $regex: cname, $options: 'i' };
    }

    if (code) {
      where.code = { $regex: code, $options: 'i' };
    }

    if (state !== undefined) {
      where.state = state === 'true' || state === true;
    }

    // 机构类型过滤 - 使用数组包含查询
    if (orgType) {
      // 如果是数组，直接使用
      if (Array.isArray(orgType)) {
        where.orgTypes = { $in: orgType.map(t => +t) };
      }
      // 如果是单个值，转换为数组
      else {
        where.orgTypes = { $in: [+orgType] };
      }
    }

    // 兼容旧的类型查询方式
    const typeFilters = [];
    if (type1 === 'true' || type1 === true) typeFilters.push(1);
    if (type2 === 'true' || type2 === true) typeFilters.push(2);
    if (type3 === 'true' || type3 === true) typeFilters.push(3);
    if (type4 === 'true' || type4 === true) typeFilters.push(4);
    if (type5 === 'true' || type5 === true) typeFilters.push(5);
    if (type6 === 'true' || type6 === true) typeFilters.push(6);
    if (type7 === 'true' || type7 === true) typeFilters.push(7);

    // 如果有旧的类型过滤条件，并且没有设置新的 orgType 参数
    if (typeFilters.length > 0 && !orgType) {
      where.orgTypes = { $in: typeFilters };
    }

    // 查询总数
    const total = await this.serviceOrgRepository.count(where);

    // 查询列表
    const list = await this.serviceOrgRepository.find({
      where,
      skip,
      take,
      order: { createTime: 'DESC' }
    });

    return { list, total };
  }

  /**
   * 根据ID获取服务机构详情
   * @param id 服务机构ID
   * @returns 服务机构详情
   */
  async findOne(id: string): Promise<ServiceOrg> {
    const serviceOrg = await this.serviceOrgRepository.findOne({ where: { _id: id } });
    if (!serviceOrg) {
      throw new HttpException('服务机构不存在', HttpStatus.NOT_FOUND);
    }
    return serviceOrg;
  }

  /**
   * 更新服务机构信息
   * @param id 服务机构ID
   * @param data 更新的数据
   * @returns 更新结果
   */
  async update(id: string, data: any): Promise<ServiceOrg> {
    const serviceOrg = await this.serviceOrgRepository.findOne({ where: { _id: id } });
    if (!serviceOrg) {
      throw new HttpException('服务机构不存在', HttpStatus.NOT_FOUND);
    }

    // 如果更新了社会信用代码，检查是否与其他机构冲突
    if (data.code && data.code !== serviceOrg.code) {
      const existingOrg = await this.serviceOrgRepository.findOne({
        where: {
          code: data.code,
          _id: { $ne: id }
        }
      });

      if (existingOrg) {
        throw new HttpException('已存在相同社会信用代码的机构', HttpStatus.BAD_REQUEST);
      }
    }

    // 更新基础信息
    if (data.cname !== undefined) serviceOrg.cname = data.cname;
    if (data.area !== undefined) serviceOrg.area = data.area;
    if (data.code !== undefined) serviceOrg.code = data.code;
    if (data.industry !== undefined) serviceOrg.industry = data.industry;
    if (data.legalPerson !== undefined) serviceOrg.legalPerson = data.legalPerson;
    if (data.legalPersonId !== undefined) serviceOrg.legalPersonId = data.legalPersonId;
    if (data.legalPersonTitle !== undefined) serviceOrg.legalPersonTitle = data.legalPersonTitle;
    if (data.zipCode !== undefined) serviceOrg.zipCode = data.zipCode;
    if (data.fax !== undefined) serviceOrg.fax = data.fax;
    if (data.email !== undefined) serviceOrg.email = data.email;
    if (data.state !== undefined) serviceOrg.state = data.state;
    if (data.remark !== undefined) serviceOrg.remark = data.remark;

    // 更新机构类型
    if (data.orgTypes !== undefined) {
      serviceOrg.orgTypes = Array.isArray(data.orgTypes) ? data.orgTypes : [];
    } else {
      // 兼容旧的布尔类型字段
      const newOrgTypes = [];
      if (data.type1) newOrgTypes.push(1);
      if (data.type2) newOrgTypes.push(2);
      if (data.type3) newOrgTypes.push(3);
      if (data.type4) newOrgTypes.push(4);
      if (data.type5) newOrgTypes.push(5);
      if (data.type6) newOrgTypes.push(6);
      if (data.type7) newOrgTypes.push(7);

      // 只有当至少有一个类型字段被设置时才更新
      if (newOrgTypes.length > 0) {
        serviceOrg.orgTypes = newOrgTypes;
      }
    }

    // 更新医疗机构信息
    if (data.medicalInstitution) {
      serviceOrg.medicalInstitution = {
        ...serviceOrg.medicalInstitution,
        ...data.medicalInstitution
      };
    }

    // 更新职业卫生技术服务机构信息
    if (data.occupationalHealthTechnicalServiceInstitution) {
      serviceOrg.occupationalHealthTechnicalServiceInstitution = {
        ...serviceOrg.occupationalHealthTechnicalServiceInstitution,
        ...data.occupationalHealthTechnicalServiceInstitution
      };
    }

    // 更新职业病健康检查机构信息
    if (data.diseaseHealthCheckInstitution) {
      serviceOrg.diseaseHealthCheckInstitution = {
        ...serviceOrg.diseaseHealthCheckInstitution,
        ...data.diseaseHealthCheckInstitution
      };
    }

    // 更新职业病诊断机构信息
    if (data.occupationalDiseaseDiagnosisInstitution) {
      serviceOrg.occupationalDiseaseDiagnosisInstitution = {
        ...serviceOrg.occupationalDiseaseDiagnosisInstitution,
        ...data.occupationalDiseaseDiagnosisInstitution
      };
    }

    // 更新放射性卫生服务机构信息
    if (data.radioHealthServiceInstitution) {
      serviceOrg.radioHealthServiceInstitution = {
        ...serviceOrg.radioHealthServiceInstitution,
        ...data.radioHealthServiceInstitution
      };
    }

    // 更新联系人信息
    if (data.contacts && Array.isArray(data.contacts)) {
      serviceOrg.contacts = data.contacts;
    }

    // 更新时间
    serviceOrg.updateTime = new Date();

    // return await this.serviceOrgRepository.save(serviceOrg);
    const updatedServiceOrg = await this.serviceOrgRepository.findOneAndUpdate(
      { _id: id },
      { $set: serviceOrg },
      { returnDocument: 'after' }
    );

    if (!updatedServiceOrg.value) {
      throw new HttpException('服务机构更新失败', HttpStatus.NOT_FOUND);
    }

    return updatedServiceOrg.value;
  }

  /**
   * 删除服务机构
   * @param ids 服务机构ID数组
   * @returns 删除结果
   */
  async remove(ids: string[]): Promise<{ deletedCount: number }> {
    const result = await this.serviceOrgRepository.deleteMany({
      _id: { $in: ids }
    });

    return { deletedCount: result.deletedCount };
  }
}
