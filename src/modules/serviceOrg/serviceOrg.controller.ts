import { Controller, Get, Post, Body, Put, Param, Delete, Query } from '@nestjs/common';
import { ServiceOrgService } from './serviceOrg.service';
import { wrapperResponse } from '../../utils/index';

@Controller('serviceOrg')
export class ServiceOrgController {
  constructor(private readonly serviceOrgService: ServiceOrgService) { }

  /**
   * 创建服务机构
   * @param data 服务机构数据
   * @returns 创建结果
   */
  @Post()
  create(@Body() data: any) {
    return wrapperResponse(
      this.serviceOrgService.create(data),
      '服务机构创建成功',
    );
  }

  /**
   * 获取服务机构列表
   * @param query 查询参数
   * @returns 服务机构列表
   */
  @Get()
  findAll(@Query() query: any) {
    return wrapperResponse(
      this.serviceOrgService.findAll(query),
      '获取服务机构列表成功',
    );
  }

  /**
   * 获取服务机构详情
   * @param id 服务机构ID
   * @returns 服务机构详情
   */
  @Get(':id')
  findOne(@Param('id') id: string) {
    return wrapperResponse(
      this.serviceOrgService.findOne(id),
      '获取服务机构详情成功',
    );
  }

  /**
   * 更新服务机构
   * @param id 服务机构ID
   * @param data 更新数据
   * @returns 更新结果
   */
  @Put(':id')
  update(@Param('id') id: string, @Body() data: any) {
    console.log(id, data);
    return wrapperResponse(
      this.serviceOrgService.update(id, data),
      '服务机构更新成功',
    );
  }

  /**
   * 删除服务机构
   * @param query 包含ids的查询参数
   * @returns 删除结果
   */
  @Delete()
  remove(@Query('ids') ids: any) {
    const idsArray = JSON.parse(JSON.stringify(ids));
    console.log(idsArray);
    return wrapperResponse(
      this.serviceOrgService.remove(idsArray),
      '服务机构删除成功',
    );
  }
}
