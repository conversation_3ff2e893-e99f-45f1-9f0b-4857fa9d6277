import { Entity, ObjectIdColumn, Column, BeforeInsert, Index } from 'typeorm';
import * as shortid from 'shortid';

// 医疗机构
class MedicalInstitution {
  @Column({ nullable: true })
  medicalInstitutionLicenseNumber: string; // 医疗机构许可证编号

  @Column({ nullable: true })
  unitType: string; // 单位类型

  @Column({ nullable: true })
  managementType: string; // 管理类型

  @Column({ nullable: true })
  unitLevel: string; // 单位级别

  @Column({ nullable: true })
  unitGrade: string; // 单位分级 (一级|二级|三级)

  @Column({ nullable: true })
  unitGrade2: string; // 单位分等 (甲等|乙等|丙等)

  @Column({ nullable: true })
  businessType: string; // 经营类型 (公立|民营)
}

// 职业卫生技术服务机构
class OccupationalHealthTechnicalServiceInstitution {
  @Column({ nullable: true })
  grade: string; // 资质 (甲级|乙级|丙级)

  @Column({ nullable: true })
  recordNo: string; // 资质证书/备案编号

  @Column({ type: 'date', nullable: true })
  validityDate: Date; // 证书有效期至

  @Column({ type: 'array', default: [] })
  itemList: string[]; // 可以开展的职业健康检查类别及项目

  @Column({ nullable: true })
  businessScope: string; // 业务范围

  @Column({ type: 'array', default: [] })
  items: any[]; // 经技术评审审核认定的检测项目

  @Column({ type: 'array', default: [] })
  persons: any[]; // 达到技术评审考核评估要求的专业技术人员名单
}

// 职业病健康检查机构
class DiseaseHealthCheckInstitution {
  @Column({ nullable: true })
  level: string; // 单位级别

  @Column({ nullable: true })
  recordNo: string; // 资质证书/备案编号

  @Column({ type: 'array', default: [] })
  itemList: string[]; // 可以开展的职业健康检查类别及项目
}

// 职业病诊断机构
class OccupationalDiseaseDiagnosisInstitution {
  @Column({ nullable: true })
  recordNo: string; // 资质证书/备案编号

  @Column({ type: 'array', default: [] })
  itemList: string[]; // 可以开展的职业健康检查类别及项目
}

// 放射性卫生服务机构
class RadioHealthServiceInstitution {
  @Column({ nullable: true })
  grade: string; // 资质

  @Column({ nullable: true })
  recordNo: string; // 资质证书/备案编号
}

// 联系人
class Contact {
  @Column()
  name: string; // 联系人姓名

  @Column({ nullable: true })
  phone: string; // 手机号码

  @Column({ nullable: true })
  fixedPhone: string; // 固定电话

  @Column({ nullable: true })
  email: string; // 电子邮箱
}

// 服务机构实体
@Entity('serviceOrgs')
export class ServiceOrg {
  @ObjectIdColumn()
  _id: string;

  @Column()
  userId: string;

  @Column()
  cname: string; // 机构名称

  @Column({ nullable: true })
  area: string; // 地区

  @Column()
  @Index()
  code: string; // 统一社会信用代码

  @Column({ nullable: true })
  industry: string; // 单位类型

  @Column({ nullable: true })
  legalPerson: string; // 法人

  @Column({ nullable: true })
  legalPersonId: string; // 法人代表身份证号码

  @Column({ nullable: true })
  legalPersonTitle: string; // 职务和职称

  @Column({ nullable: true })
  zipCode: string; // 邮政编码

  @Column({ nullable: true })
  fax: string; // 传真号码

  @Column({ nullable: true })
  email: string; // 电子邮箱

  @Column({ default: true })
  state: boolean; // 是否启用

  @Column({ nullable: true })
  remark: string; // 备注

  // 机构类型 - 使用数字数组存储
  // 1: 职业卫生技术服务机构
  // 2: 职业病健康检查机构
  // 3: 职业病诊断机构
  // 4: 职业病鉴定机构
  // 5: 放射性卫生服务机构
  // 6: 医疗机构
  // 7: 疾控中心
  @Column({ type: 'array', default: [] })
  orgTypes: number[];

  // 机构详细信息
  @Column(_type => MedicalInstitution)
  medicalInstitution: MedicalInstitution; // 医疗机构

  @Column(_type => OccupationalHealthTechnicalServiceInstitution)
  occupationalHealthTechnicalServiceInstitution: OccupationalHealthTechnicalServiceInstitution; // 职业卫生技术服务机构

  @Column(_type => DiseaseHealthCheckInstitution)
  diseaseHealthCheckInstitution: DiseaseHealthCheckInstitution; // 职业病健康检查机构

  @Column(_type => OccupationalDiseaseDiagnosisInstitution)
  occupationalDiseaseDiagnosisInstitution: OccupationalDiseaseDiagnosisInstitution; // 职业病诊断机构

  @Column(_type => RadioHealthServiceInstitution)
  radioHealthServiceInstitution: RadioHealthServiceInstitution; // 放射性卫生服务机构

  // 联系人列表
  @Column(_type => Contact)
  contacts: Contact[]; // 联系人列表

  @Column({ default: () => new Date() })
  createTime: Date;

  @Column({ default: () => new Date() })
  updateTime: Date;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }

  constructor() {
    this.medicalInstitution = new MedicalInstitution();
    this.occupationalHealthTechnicalServiceInstitution = new OccupationalHealthTechnicalServiceInstitution();
    this.diseaseHealthCheckInstitution = new DiseaseHealthCheckInstitution();
    this.occupationalDiseaseDiagnosisInstitution = new OccupationalDiseaseDiagnosisInstitution();
    this.radioHealthServiceInstitution = new RadioHealthServiceInstitution();
    this.contacts = [];
  }
}
