import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ServiceOrgController } from './serviceOrg.controller';
import { ServiceOrgService } from './serviceOrg.service';
import { ServiceOrg } from './mongo/serviceOrg.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([ServiceOrg], 'mongodbConnection'),
  ],
  controllers: [ServiceOrgController],
  providers: [ServiceOrgService],
  exports: [ServiceOrgService],
})
export class ServiceOrgModule { }
