import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { Employees } from './mongo/employees.entity';
import { HealthCheckRegister } from './mongo/health-check-register.entity';
import { HealthCheckItemInfo } from './mongo/health-check-item-info.entity';
import { Adminorgs } from '../enterprise/mongo/adminorgs.entity';
import { OccupationalHistory } from './mongo/occupationalHistory.entity'; // 劳动者工作经历
import { DeterminationRecord } from './mongo/determinationRecord.entity'; // 职业病鉴定记录
import { DiagnosticRecord } from './mongo/diagnosticRecord.entity'; // 职业病诊断记录

@Injectable()
export class EmployeeService {
  constructor(
    @InjectRepository(Employees, 'mongodbConnection')
    private employeeRepository: MongoRepository<Employees>,
    @InjectRepository(HealthCheckRegister, 'mongodbConnection')
    private HealthCheckRegisterRepository: MongoRepository<HealthCheckRegister>,
    @InjectRepository(HealthCheckItemInfo, 'mongodbConnection')
    private HealthCheckItemInfoRepository: MongoRepository<HealthCheckItemInfo>,
    @InjectRepository(Adminorgs, 'mongodbConnection')
    private AdminorgsRepository: MongoRepository<Adminorgs>,
    @InjectRepository(OccupationalHistory, 'mongodbConnection')
    private OccupationalHistoryRepository: MongoRepository<OccupationalHistory>,
    @InjectRepository(DeterminationRecord, 'mongodbConnection')
    private DeterminationRecordRepository: MongoRepository<DeterminationRecord>,
    @InjectRepository(DiagnosticRecord, 'mongodbConnection')
    private DiagnosticRecordRepository: MongoRepository<DiagnosticRecord>,

  ) { }

  async list(
    superUserInfo: any,
    query: { name?: string, EnterpriseID?: string, pageNum?: number, pageSize?: number }
  ): Promise<{ list: Employees[], total: number }> {
    const { name, EnterpriseID, pageNum = 1, pageSize = 10 } = query;
    const match = <any>{
      // districtRegAdd: { $all: superUserInfo.regAdd }
    }
    // 如果有name参数，按name字段模糊查询
    if (name) {
      match.name = { $regex: name };
    }
    if (EnterpriseID) {
      match.EnterpriseID = query.EnterpriseID;
    }
    const pipeline = [
      { $match: match },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'EnterpriseID',
          foreignField: '_id',
          as: 'EnterpriseID'
        }
      },
      { $unwind: { path: '$EnterpriseID', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'EnterpriseID.districtRegAdd': { $all: superUserInfo.regAdd }
        }
      },
      {
        $facet: {
          list: [
            { $sort: { createdAt: -1 } },
            { $skip: (pageNum - 1) * (+pageSize) },
            { $limit: +pageSize }
          ],
          total: [
            { $count: 'total' }
          ]
        }
      },
      {
        $project: {
          list: 1,
          total: { $arrayElemAt: ['$total.total', 0] }
        }
      }
    ];

    const result = await this.employeeRepository.aggregate(pipeline).toArray();
    let res: any = { list: [], total: 0 };

    if (result.length > 0) {
      res = result[0];
      if (!res.total) {
        res.total = 0;
      }
    }

    return res;
  }

  async findOne(_id: string): Promise<Employees> {
    // 通过EnterpriseID字段左外连接adminorgs表获取企业信息
    // const employee = await this.employeeRepository.findOne({ where: { _id }, relations: ['EnterpriseID'] });
    const employee = await this.employeeRepository.aggregate([
      { $match: { _id } },
      {
        $lookup: {
          from: 'adminorgs',
          localField: 'EnterpriseID',
          foreignField: '_id',
          as: 'EnterpriseID'
        }
      },
      { $unwind: { path: '$EnterpriseID', preserveNullAndEmptyArrays: true } },
      { $project: { _id: 1, name: 1, IDNum: 1, gender: 1, phoneNum: 1, cname: '$EnterpriseID.cname', districtRegAdd: '$EnterpriseID.districtRegAdd' } }
    ]).toArray().then(results => results[0]);
    if (!employee) {
      throw new HttpException('Employee not found', HttpStatus.NOT_FOUND);
    }
    return employee;
  }

  async employeeRegisterlist(query: { employeeID?: string, EnterpriseID?: string, pageNum?: number, pageSize?: number }): Promise<HealthCheckRegister[]> {
    const { employeeID, EnterpriseID, pageNum = 1, pageSize = 10 } = query;

    const where = {
      status: 3 // 已完成
    }
    if (employeeID) {
      where['employeeID'] = employeeID;
    }
    if (EnterpriseID) {
      where['EnterpriseID'] = EnterpriseID;
    }
    const pipeline = [
      { $match: where },
      {
        $facet: {
          list: [
            { $sort: { createdAt: -1 } },
            { $skip: (pageNum - 1) * (+pageSize) },
            { $limit: +pageSize }
          ],
          total: [
            { $count: 'total' }
          ]
        }
      },
      {
        $project: {
          list: 1,
          total: { $arrayElemAt: ['$total.total', 0] }
        }
      }
    ];
    const list = await this.HealthCheckRegisterRepository.aggregate(pipeline).toArray();
    let res: any = { list: [], total: 0 };
    if (list.length > 0) {
      res = list[0];
      if (!res.total) {
        res.total = 0;
      }
    }
    return res;
    // const list = await this.HealthCheckRegisterRepository.find({ where: { employeeID: _id } });
    // if (!list) {
    //   throw new HttpException('employee Register not found', HttpStatus.NOT_FOUND);
    // }
    // return list;
  }

  async itemList(): Promise<HealthCheckItemInfo[]> {
    const list = await this.HealthCheckItemInfoRepository.find();
    return list;
  }

  async workHistory(query: { employeeID?: string, EnterpriseID?: string, pageNum?: number, pageSize?: number }): Promise<any> {

    const where = {}
    if (query.employeeID) {
      where['employeeId'] = query.employeeID;
    }
    if (query.EnterpriseID) {
      where['EnterpriseID'] = query.EnterpriseID;
    }
    const { pageNum = 1, pageSize = 10 } = query;
    const pipeline = [
      { $match: where },
      {
        $facet: {
          list: [
            { $sort: { createdAt: -1 } },
            { $skip: (pageNum - 1) * (+pageSize) },
            { $limit: +pageSize }
          ],
          total: [
            { $count: 'total' }
          ]
        }
      },
      {
        $project: {
          list: 1,
          total: { $arrayElemAt: ['$total.total', 0] }
        }
      }
    ];
    const result = await this.OccupationalHistoryRepository.aggregate(pipeline).toArray();
    let res: any = { list: [], total: 0 };
    if (result.length > 0) {
      res = result[0];
      if (!res.total) {
        res.total = 0;
      }
    }
    return res;
  }

  // 职业病诊断记录
  async diagnosticRecordList(query: { idNumber?: string, pageNum?: number, pageSize?: number }): Promise<any> {
    const where = {}
    if (query.idNumber) {
      where['idNumber'] = query.idNumber;
    }

    const { pageNum = 1, pageSize = 10 } = query;
    const pipeline = [
      { $match: where },
      {
        $facet: {
          list: [
            { $sort: { createdAt: -1 } },
            { $skip: (pageNum - 1) * (+pageSize) },
            { $limit: +pageSize }
          ],
          total: [
            { $count: 'total' }
          ]
        }
      },
      {
        $project: {
          list: 1,
          total: { $arrayElemAt: ['$total.total', 0] }
        }
      }
    ];
    const result = await this.DiagnosticRecordRepository.aggregate(pipeline).toArray();
    let res: any = { list: [], total: 0 };
    if (result.length > 0) {
      res = result[0];
      if (!res.total) {
        res.total = 0;
      }
    }
    return res;
  }

  // 职业病鉴定记录
  async determinationRecordList(query: { idNumber?: string, pageNum?: number, pageSize?: number }): Promise<any> {
    const where = {}
    if (query.idNumber) {
      where['idNumber'] = query.idNumber;
    }
    const { pageNum = 1, pageSize = 10 } = query;
    const pipeline = [
      { $match: where },
      {
        $facet: {
          list: [
            { $sort: { createdAt: -1 } },
            { $skip: (pageNum - 1) * (+pageSize) },
            { $limit: +pageSize }
          ],
          total: [
            { $count: 'total' }
          ]
        }
      },
      {
        $project: {
          list: 1,
          total: { $arrayElemAt: ['$total.total', 0] }
        }
      }
    ];
    const result = await this.DeterminationRecordRepository.aggregate(pipeline).toArray();
    let res: any = { list: [], total: 0 };
    if (result.length > 0) {
      res = result[0];
      if (!res.total) {
        res.total = 0;
      }
    }
    return res;
  }

}
