import { Controller, Get, Query, Param, } from '@nestjs/common';
import { EmployeeService } from './employee.service';
import { wrapperResponse } from '../../utils/index';
import { Isolation, superUserInfo } from '../auth/isolation.decorator'; // 数据隔离

@Controller('employee')
export class EmployeeController {
  constructor(private readonly EmployeeService: EmployeeService) { }

  @Get()
  @Isolation()
  async getEmployees(
    @superUserInfo() superUserInfo: any,
    @Query() query: { name?: string, EnterpriseID?: string, pageNum?: number, pageSize?: number }
  ) {
    return wrapperResponse(
      this.EmployeeService.list(superUserInfo, query),
      '劳动者列表成功',
    );
  }

  @Get('/registerlist')
  async employeeRegisterlist(@Query() query: { employeeID?: string, EnterpriseID?: string, pageNum?: number, pageSize?: number }) {
    return wrapperResponse(
      this.EmployeeService.employeeRegisterlist(query),
      '获取劳动者健康检查列表成功',
    );
  }

  @Get('/workHistoryList')
  async workHistory(@Query() query: { employeeID?: string, EnterpriseID?: string, pageNum?: number, pageSize?: number }) {
    return wrapperResponse(
      this.EmployeeService.workHistory(query),
      '获取劳动者工作经历成功',
    );
  }

  @Get('/diagnosticRecordList')
  async diagnosticRecordList(@Query() query: { idNumber?: string, pageNum?: number, pageSize?: number }) {
    return wrapperResponse(
      this.EmployeeService.diagnosticRecordList(query),
      '获取劳动者职业病诊断记录成功',
    );
  }

  @Get('/determinationRecordList')
  async determinationRecordList(@Query() query: { idNumber?: string, pageNum?: number, pageSize?: number }) {
    return wrapperResponse(
      this.EmployeeService.determinationRecordList(query),
      '获取劳动者职业病鉴定记录成功',
    );
  }

  @Get('/itemList')
  async itemList() {
    return wrapperResponse(
      this.EmployeeService.itemList(),
      '获取体检项目成功',
    );
  }

  @Get(':id')
  async getEmployee(@Param('id') id: string) {
    return wrapperResponse(
      this.EmployeeService.findOne(id),
      '获取劳动者详情成功',
    );
  }




}
