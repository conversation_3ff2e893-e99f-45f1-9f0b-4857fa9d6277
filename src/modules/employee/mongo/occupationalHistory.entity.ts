import { Enti<PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>umn, ObjectIdColumn } from 'typeorm';
import { Employees } from './employees.entity';

@Entity('occupationalHistories')
export class OccupationalHistory {
  @ObjectIdColumn()
  _id: string;

  @Column()
  @ManyToOne(() => Employees)
  @JoinColumn()
  employeeId: string;

  @Column({ nullable: true })
  entryTime: string; // 入职时间

  @Column({ nullable: true })
  leaveTime: string; // 离职时间

  @Column()
  workUnit: string; // 工作单位

  @Column({ nullable: true })
  workshop: string; // 车间

  @Column({ nullable: true })
  station: string; // 岗位

  @Column({ nullable: true })
  workType: string; // 工种

  @Column({
    type: 'enum',
    enum: ['1', '2'],
    nullable: true
  })
  hisType: '1' | '2'; // 1.放射/2.非放射

  @Column({ nullable: true })
  exposureToHarmfulFactors: string; // 接触有害因素

  @Column({ nullable: true })
  prfwrklod: string; // (放射)每日工作时数或工作量

  @Column({ nullable: true })
  prfshnvlu: string; // (放射)职业史累积受照剂量

  @Column({ nullable: true })
  prfexcshn: string; // (放射)职业史过量照射史

  @Column({ nullable: true })
  prfraysrt2: string; // (放射)职业照射种类

  @Column({ nullable: true })
  prfraysrtcods: string; // (放射)职业照射种类代码

  @Column({ nullable: true })
  fsszl: string; // (放射)放射线种类

  @Column({ nullable: true })
  defendStep: string; // (非放射)防护措施

  @Column({ nullable: true })
  chkdat: string; // 检查日期

  @Column({ nullable: true })
  chkdoct: string; // 检查医生
}