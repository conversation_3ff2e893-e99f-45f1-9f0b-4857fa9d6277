import { Entity, ObjectIdColumn, Column, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';
@Entity('dingtrees')
export class Dingtrees {
  @ObjectIdColumn()
  _id: string;

  @Column({ type: 'varchar', default: '', comment: '编码' })
  unitCode: string;

  @Column('varchar', { comment: '企业id' })
  EnterpriseID: string;

  @Column('varchar', { comment: '部门名称' })
  name: string; // 部门名称

  @Column('varchar', { comment: '简称' })
  shortName: string; // 简称

  @Column('varchar', { comment: '父级id' })
  parentId: string; // 父级id

  @Column('varchar')
  type: string;

  @Column({ type: 'int', default: 0 })
  sortId: number;

  // @OneToMany(() => Employees, (employee) => employee.departs)
  // staff: Employees[]; // 员工
  @Column({ type: 'array', default: [] })
  staff: string[];

  @Column({ type: 'boolean', default: false })
  topLevelOfTheEnterprise: boolean; // 是否为企业顶级部门

  @Column({ type: 'boolean', default: false })
  isDelete: boolean; // 假删

  // 创建时间
  @Column({ type: 'date', default: new Date() })
  createTime: Date;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }

  constructor() {
    this.staff = this.staff || [];
  }
}
