import { Entity, ObjectIdColumn, ObjectId, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { shortid } from 'shortid';

@Entity('healthcheckregisters')
export class HealthCheckRegister {
  @ObjectIdColumn()
  _id: shortid;

  @Column()
  checkNo: string;

  @Column()
  employeeID: string;

  @Column()
  EnterpriseID: string;

  @Column()
  physicalOrgID: string;

  @Column()
  contractID: string;

  @Column()
  examPlanID: string;

  @Column({ type: 'number', enum: [0, 1, 2, 3, 4], default: 0 })
  status: number;

  @Column({ nullable: true })
  auditReason: string;

  @Column()
  name: string;

  @Column({ type: 'string', enum: [1, 2], nullable: true })
  gender: string;

  @Column({ type: 'string', enum: [1, 2, 3, 4, 5, 6, 7, 99], default: 1 })
  idType: string;

  @Column()
  idNumber: string;

  @Column({ type: 'date', nullable: true })
  birthDate: Date;

  @Column({ type: 'string', enum: [10, 20, 21, 22, 23, 30, 40, 90], default: 10 })
  maritalStatus: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  address: string;

  @Column({ nullable: true })
  emergencyContact: string;

  @Column({ nullable: true })
  emergencyPhone: string;

  @Column({ type: 'string', enum: [1, 2], nullable: true })
  checkType: string;

  @Column({ nullable: true })
  department: string;

  @Column({ nullable: true })
  workType: string;

  @Column('simple-array', { nullable: true })
  hazardFactors: string[];

  @Column('simple-array', { nullable: true })
  checkHazardFactors: Array<{
    name: string;
    conclusion: number;
    disease: string[];
    taboo: string;
    recheckTime: Date;
    recheckProjects: string[];
  }>;

  @Column({ type: 'number', default: 0 })
  totalWorkYears: number;

  @Column({ type: 'number', default: 0 })
  totalWorkMonths: number;

  @Column({ type: 'number', default: 0 })
  exposureWorkYears: number;

  @Column({ type: 'number', default: 0 })
  exposureWorkMonths: number;

  @Column({ type: 'string', enum: [1, 2, 3, 4, 5], nullable: true })
  examType: string;

  @Column({ type: 'boolean', nullable: true })
  isRecheck: boolean;

  @Column({ nullable: true })
  recheckNo: string;

  @Column({ type: 'number', nullable: true })
  totalPrice: number;

  @Column({ nullable: true })
  facePhoto: string;

  @Column('simple-array', { nullable: true })
  checkDepartments: Array<{
    departmentId: string;
    departmentName: string;
    checkProjects: Array<{
      projectId: string;
      projectName: string;
      projectPrice: number;
      checkItems: Array<{
        itemId: string;
        result: string;
        conclusion: string;
      }>;
    }>;
    summary: string;
    status: number;
  }>;

  @Column({ nullable: true })
  healthSummary: string;

  @Column({ nullable: true })
  suggestion: string;

  @Column({ nullable: true })
  jobSummary: string;

  @Column('simple-array', { nullable: true })
  jobConclusion: any[];

  @Column({ type: 'date', nullable: true })
  registerTime: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 