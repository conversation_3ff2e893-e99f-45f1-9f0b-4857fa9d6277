import { Entity, Column, ObjectIdColumn, BeforeInsert } from 'typeorm';
// import { ObjectId } from 'mongodb';
import * as shortid from 'shortid';

class occupationalDisease {
  @Column({ type: 'varchar', comment: '职业病名称' })
  name: string;

  @Column({ type: 'varchar', comment: '职业病编码' })
  code: string;
};
/**
 * 鉴定记录实体
 * 包含鉴定类别（首次鉴定、再鉴定）、用人单位名称、用人单位统一社会信用代码、用工单位名称、用工单位统一社会信用代码、
 * 申请鉴定主要理由、鉴定依据、鉴定结论、诊断鉴定委员会、鉴定日期、劳动者名称、身份证、对应的诊断编号
 */
@Entity('determinationRecords')
export class DeterminationRecord {
  @ObjectIdColumn()
  _id: string;

  /**
   * 鉴定类别（枚举）
   * 1: 首次鉴定
   * 2: 再鉴定
   */
  @Column({
    type: 'enum',
    enum: ['1', '2'],
    comment: '鉴定类别：1首次鉴定 2再鉴定'
  })
  determinationCategory: string;

  @Column({ type: 'varchar', comment: '用人单位名称' })
  employerName: string;

  @Column({ type: 'varchar', comment: '用人单位统一社会信用代码', nullable: true })
  employerCreditCode: string;

  @Column({ type: 'varchar', comment: '用工单位名称', nullable: true })
  laborEmployerName: string;

  @Column({ type: 'varchar', comment: '用工单位统一社会信用代码', nullable: true })
  laborEmployerCreditCode: string;

  @Column({ type: 'varchar', comment: '申请鉴定主要理由', nullable: true })
  applicationReason: string;

  @Column({ type: 'varchar', comment: '鉴定依据', nullable: true })
  determinationBasis: string;

  @Column({ type: 'boolean', comment: '鉴定结论，false代表不是职业病，true代表是职业病' })
  hasOccupationalDisease: boolean;

  @Column({ type: 'varchar', comment: '鉴定结论描述', nullable: true })
  determinationConclusionDescription: string;

  @Column(_type => occupationalDisease)
  occupationalDisease: occupationalDisease[] = [];


  @Column({ type: 'varchar', comment: '诊断鉴定委员会', nullable: true })
  determinationCommittee: string;

  @Column({ type: 'date', comment: '鉴定日期' })
  determinationDate: Date;

  @Column({ type: 'varchar', comment: '劳动者名称' })
  workerName: string;

  @Column({ type: 'varchar', comment: '手机号', nullable: true })
  phone: string;

  @Column({
    type: 'enum',
    enum: ['1', '2'],
    comment: '性别 1男 2女',
    nullable: true
  })
  gender: string;

  @Column({ type: 'varchar', comment: '身份证号码' })
  idNumber: string;

  @Column({ type: 'varchar', comment: '对应的诊断编号', nullable: true })
  diagnosisNumber: string;

  @Column({ type: 'varchar', comment: '鉴定编号', unique: true })
  determinationNumber: string;

  @Column({ type: 'varchar', comment: '(再鉴定对应的)首次鉴定编号', nullable: true })
  firstDeterminationNumber: string;

  @Column({ type: 'date', default: () => new Date(), comment: '创建时间' })
  createdAt: Date;

  @Column({ type: 'date', default: () => new Date(), comment: '更新时间' })
  updatedAt: Date;

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}
