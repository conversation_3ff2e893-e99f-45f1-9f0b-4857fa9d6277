import { Entity, Column, ObjectIdColumn, CreateDateColumn, UpdateDateColumn, BeforeInsert } from 'typeorm';
import { shortid } from 'shortid';

@Entity('HealthCheckItemInfos')
export class HealthCheckItemInfo {
  @ObjectIdColumn()
  _id: shortid;

  @Column()
  projectName: string;     // 检查项目名称

  @Column()
  projectNumber: string;    // 检查项目编号

  @Column()
  marriage: number;         // 是否限制婚姻

  @Column()
  genderLimit: number;      // 是否限制性别

  @Column()
  isLimitAge: number;      // 是否限制年龄

  @Column()
  ageLimitMin: number;    // 年龄最小值

  @Column()
  ageLimitMax: number;    // 年龄最大值

  @Column()
  standardValueMin: number;    // 最小极值

  @Column()
  standardValueMax: number;    // 最大极值

  @Column()
  referenceMin: string;    // 参考范围

  @Column()
  referenceMax: string;    // 参考范围

  @Column()
  highValueTips: string;    // 偏高提示

  @Column()
  lowValueTips: string;    // 偏低提示

  @Column()
  msrunt: string;      // 计量单位

  @Column()
  resultType: string;

  @Column()
  suitType: string[];     // 适用类型

  @Column()
  createTime: Date;   // 创建时间

  @Column()
  updateTime: string;     // 更新时间
  // 关联体检机构
  // @ManyToOne(() => PhysicalExamOrg)
  // @JoinColumn({ name: 'physical_org_id' })
  @Column()
  physicalOrg: String;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

}