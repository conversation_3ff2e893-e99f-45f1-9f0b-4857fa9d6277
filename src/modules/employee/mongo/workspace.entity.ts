import { <PERSON><PERSON><PERSON>, <PERSON>umn, CreateDateC<PERSON>umn, UpdateDateColumn, ManyToOne, JoinColumn, ObjectIdColumn } from 'typeorm';
import { Adminorgs } from '../../enterprise/mongo/adminorgs.entity';
import { Employees } from './employees.entity';

@Entity('workspace')
export class Workspace {
  @ObjectIdColumn()
  _id: string;

  @Column({ name: 'EnterpriseID' })
  @ManyToOne(() => Adminorgs)
  @JoinColumn({ name: 'EnterpriseID' })
  EnterpriseID: string;

  @Column({ nullable: true })
  // @ManyToOne(() => ServiceOrg)
  // @JoinColumn({ name: 'serviceOrgId' })
  serviceOrgId: string;

  @Column({ nullable: true })
  // @ManyToOne(() => PhysicalExamOrg)
  // @JoinColumn({ name: 'physicalExamOrgId' })
  physicalExamOrgId: string;

  @Column({
    type: 'enum',
    enum: ['0', '1'],
    default: '0'
  })
  status: string;

  @Column({
    type: 'varchar',
    default: '',
  })
  workshopName: string;

  @Column()
  workspaceName: string;

  @Column()
  workTypeName: string;

  @Column({ type: 'int', nullable: true })
  exposedPeopleNumber: number;

  @Column({ nullable: true })
  dailyProduce: string;

  @Column({ type: 'int', nullable: true })
  workDays: number;

  @Column({ type: 'float', nullable: true })
  exposureHours: number;

  @Column('jsonb')
  employees: Array<{
    _id: string;
    employeeId: string;
    confirmTransfer: boolean;
  }>;

  @Column('jsonb')
  stations: Array<{
    _id: string;
    stationName: string;
    shiftContactTime: number;
    workDayWeek: string;
    weeklyExposureHours: number;
    harmFactors: string[];
    workWay: string;
    protectiveEquipment: string;
    protectiveFacilities: string;
    equipCount: number;
  }>;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}