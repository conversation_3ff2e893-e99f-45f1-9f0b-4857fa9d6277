import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmployeeController } from './employee.controller';
import { EmployeeService } from './employee.service';
import { Employees } from './mongo/employees.entity';
import { Adminorgs } from '../enterprise/mongo/adminorgs.entity';
import { HealthCheckRegister } from './mongo/health-check-register.entity';
import { HealthCheckItemInfo } from './mongo/health-check-item-info.entity';
import { OccupationalHistory } from './mongo/occupationalHistory.entity'; // 劳动者工作经历
import { DeterminationRecord } from './mongo/determinationRecord.entity'; // 职业病鉴定记录
import { DiagnosticRecord } from './mongo/diagnosticRecord.entity'; // 职业病诊断记录


// 用户管理模块
@Module({
  imports: [
    TypeOrmModule.forFeature([Employees, HealthCheckRegister, HealthCheckItemInfo, Adminorgs,
      OccupationalHistory, DeterminationRecord, DiagnosticRecord
    ], 'mongodbConnection'),
  ],
  controllers: [EmployeeController],
  providers: [EmployeeService],
  exports: [EmployeeService],
})
export class EmployeeModule { }
