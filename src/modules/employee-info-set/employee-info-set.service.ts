import { Injectable } from '@nestjs/common';
import { CreateInfoSetDto } from './dto/create-info-set.dto';
import { UpdateInfoSetDto } from './dto/update-info-set.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InfoSet } from './entities/employee-info-set.entity';
import { Like } from 'typeorm';

@Injectable()
export class InfoSetService {
  constructor(
    @InjectRepository(InfoSet, 'mysqlConnection')
    private InfoSetRepository: Repository<InfoSet>,
  ) { }

  async create(createInfoSetDto: any, userInfo) {
    createInfoSetDto.userId = userInfo.id;
    return await this.InfoSetRepository.save(createInfoSetDto);
  }

  async findAll() {
    return await this.InfoSetRepository.find();
  }

  // 分页查询
  async list(userInfo: any,page: number, pageSize: number, status?: boolean, name?: string ) {
    const where: any = {
      userId: userInfo.id
    };
    if (name) {
      where.name = Like(`%${name}%`)
    }
    if (status) {
      where.status = status;
    }
    const list = await this.InfoSetRepository.find({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where
    })

    const total = await this.InfoSetRepository.count()

    return {
      list,
      total,
    };
  }

  async findOne(id: number) {
    return await this.InfoSetRepository.findOne({ where: { id } });
  }

  async update(id: number, updateInfoSetDto: UpdateInfoSetDto) {
    return await this.InfoSetRepository.update({ id }, { ...updateInfoSetDto });
  }

  async remove(ids: number[]) {
    return await this.InfoSetRepository.delete(ids);
  }
}
