import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('employeeInfoSet')
export class InfoSet {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  userId: string;

  @Column({
    type: 'varchar',
    comment: '信息集名称'
  })
  name: string;

  @Column({
    default: true,
    comment: '工作经历信息'
  })
  workHistory: boolean;

  @Column({
    default: true,
    comment: '历次职业病危害检测信息'
  })
  diseaseDetection: boolean;

  @Column({
    default: true,
    comment: '历次健康体检信息'
  })
  examination: boolean;

  @Column({
    default: true,
    comment: '历次职业病鉴定信息'
  })
  diseaseDetermination: boolean;

  @Column({
    default: true,
    comment: '劳动者职业病发生情况信息'
  })
  diseaseIncidence: boolean;

  @Column({
    default: true,
    comment: '是否启用'
  })
  status: boolean;

  @CreateDateColumn({ comment: '创建时间', type: 'datetime' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间', type: 'datetime' })
  updatedAt: Date;
}