import { Controller, Get, Post, Body, Param, Delete, Put, Query } from '@nestjs/common';
import { InfoSetService } from './employee-info-set.service';
import { CreateInfoSetDto } from './dto/create-info-set.dto';
import { UpdateInfoSetDto } from './dto/update-info-set.dto';
import { wrapperResponse } from '../../utils/index';
import { Isolation, userInfo } from '../auth/isolation.decorator'; // 数据隔离


@Controller('infoSet')
export class InfoSetController {
  constructor(private readonly infoSetService: InfoSetService) { }

  @Post()
  @Isolation()
  create(@Body() createInfoSetDto: any, @userInfo() userInfo: any) {
    const { id, ...data } = createInfoSetDto;
    return wrapperResponse(
      this.infoSetService.create(data, userInfo),
      '信息集创建成功',
    );
  }

  @Get()
  @Isolation()
  list(
    @Query() query: { page: number, pageSize: number, status?: boolean, name?: string },
    @userInfo() userInfo: any
  ) {
    return wrapperResponse(
      this.infoSetService.list(userInfo, query.page || 1, query.pageSize || 10, query.status, query.name),
      '查询信息集列表成功',
    );
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return wrapperResponse(
      this.infoSetService.findOne(+id),
      '获取信息集详情成功',
    );
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateInfoSetDto: UpdateInfoSetDto) {
    return wrapperResponse(
      this.infoSetService.update(+id, updateInfoSetDto),
      '信息集编辑成功',
    );
  }

  @Delete()
  remove(@Query() query: { ids: number[] }) {
    console.log(query.ids);
    const idsArr = query.ids.map(id => +id);
    return wrapperResponse(
      this.infoSetService.remove(idsArr),
      '信息集删除成功',
    );
  }
}
