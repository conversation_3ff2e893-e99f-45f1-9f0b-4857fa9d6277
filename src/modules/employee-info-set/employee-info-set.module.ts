import { Module } from '@nestjs/common';
import { InfoSetService } from './employee-info-set.service';
import { InfoSetController } from './employee-info-set.controller';
import { InfoSet } from './entities/employee-info-set.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([InfoSet], 'mysqlConnection')],
  controllers: [InfoSetController],
  providers: [InfoSetService],
  exports: [InfoSetService],
})
export class InfoSetModule {}
