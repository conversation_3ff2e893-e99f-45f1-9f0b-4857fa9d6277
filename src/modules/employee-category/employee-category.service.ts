import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmployeeCategory } from './entities/employee-category.entity';
import { Like } from 'typeorm';
@Injectable()
export class EmployeeCategoryService {
  constructor(
    @InjectRepository(EmployeeCategory, 'mysqlConnection')
    private employeeCategoryRepository: Repository<EmployeeCategory>,
  ) { }

  async create(data: any, userInfo: any) {
    const { name, area, harmfactor, industry, enterprise } = data;
    const paload = { name, area, harmfactor, industry, enterprise, state: 1, userId: userInfo.id };
    return await this.employeeCategoryRepository.save(paload);
  }

  async findAll() {
    return await this.employeeCategoryRepository.find();
  }

  // 分页查询
  async list(userInfo,page: number, pageSize: number, state?: number, name?: string) {
    const where: any = {
      userId: userInfo.id
    };
    if (name) {
      where.name = Like(`%${name}%`)
    }
    if (state) {
      where.state = state;
    }
    const list = await this.employeeCategoryRepository.find({
      skip: (page - 1) * pageSize,
      take: pageSize,
      where,
    });

    const total = await this.employeeCategoryRepository.count();

    return {
      list,
      total,
    };
  }

  async findOne(id: number) {
    return await this.employeeCategoryRepository.findOne({ where: { id } });
  }

  async update(id: number, data: any) {
    return await this.employeeCategoryRepository.update({ id }, data);
  }

  async remove(ids: number[]) {
    return await this.employeeCategoryRepository.delete(ids);
  }
}
