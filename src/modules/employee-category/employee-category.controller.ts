import { Controller, Get, Post, Body, Put, Param, Delete, Query } from '@nestjs/common';
import { EmployeeCategoryService } from './employee-category.service';
import { wrapperResponse } from '../../utils/index';
import { Isolation, userInfo } from '../auth/isolation.decorator'; // 数据隔离

@Controller('employeeCategory')
export class EmployeeCategoryController {
  constructor(private readonly employeeCategoryService: EmployeeCategoryService) { }

  @Post()
  @Isolation()
  create(@Body() data: any, @userInfo() userInfo: any) {
    return wrapperResponse(
      this.employeeCategoryService.create(data, userInfo),
      '劳动者类别创建成功',
    );
  }

  @Get()
  @Isolation()
  list(@Query() query: { page: number, pageSize: number, state?: number, name?: string }, @userInfo() userInfo: any) {
    return wrapperResponse(
      this.employeeCategoryService.list(userInfo, query.page || 1, query.pageSize || 10, query.state, query.name),
      '查询劳动者类别列表成功',
    );
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return wrapperResponse(
      this.employeeCategoryService.findOne(+id),
      '获取劳动者类别详情成功',
    );
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() data: any) {
    return wrapperResponse(
      this.employeeCategoryService.update(+id, data),
      '劳动者类别编辑成功',
    );
  }

  @Delete()
  remove(@Query() query: { ids: number[] }) {
    const idsArr = query.ids.map(id => +id);
    return wrapperResponse(
      this.employeeCategoryService.remove(idsArr),
      '劳动者类别删除成功',
    );
  }
}
