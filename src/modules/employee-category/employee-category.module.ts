import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmployeeCategoryService } from './employee-category.service';
import { EmployeeCategoryController } from './employee-category.controller';
import { EmployeeCategory } from './entities/employee-category.entity';

@Module({
  imports: [TypeOrmModule.forFeature([EmployeeCategory], 'mysqlConnection')],
  controllers: [EmployeeCategoryController],
  providers: [EmployeeCategoryService],
  exports: [EmployeeCategoryService],
})
export class EmployeeCategoryModule {}
