import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('employeeCategory')
export class EmployeeCategory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  userId: string;

  @Column()
  name: string;

  @Column()
  area: string;

  @Column()
  harmfactor: string;

  @Column()
  industry: string;

  @Column()
  enterprise: string;

  @Column({
    default: 1,
    comment: '是否启用' // 1 启用 0 禁用
  })
  state: number;

  @CreateDateColumn({ comment: '创建时间', type: 'datetime' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间', type: 'datetime' })
  updatedAt: Date;
}
