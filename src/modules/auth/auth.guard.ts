import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { IS_PUBLIC_KEY } from './public.decorator';
import { IS_ISOLATION_KEY } from './isolation.decorator';
import { UserService } from '../user/user.service';
import { AuthService } from './auth.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private configService: ConfigService,
    private userService: UserService, // 添加用户服务
    private authService: AuthService,
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    // console.log('🍊 request=====', request);
    const token = extractTokenFromHeader(request);
    // console.log('🍊 token=====', token);
    if (!token) {
      throw new UnauthorizedException('请先登录');
    }

    try {
      // 验证token
      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get('jwtModule').secrect,
      });

      // 角色权限验证
      // const routeRoles = [
      //   ...(this.reflector.get<string[]>('roles', context.getClass()) || []),
      //   ...(this.reflector.get<string[]>('roles', context.getHandler()) || []),
      // ];

      // if (!payload.role) {
      //   throw new UnauthorizedException('您的账户未分配角色，请联系管理员');
      // }

      // if (routeRoles.length && !routeRoles.includes(payload.role)) {
      //   throw new UnauthorizedException('您没有权限访问该接口');
      // }

      // 数据隔离
      const isIsolation = this.reflector.getAllAndOverride(IS_ISOLATION_KEY, [
        context.getHandler(),
        context.getClass(),
      ]);
      if (isIsolation) {
        // 查询当前账号的memeberId
        const user = await this.userService.findOneById(payload.userId);
        if (!user.openId) {
          throw new UnauthorizedException('您的账号未绑定监管单位，请联系管理员');
        }
        request.userInfo = user;
        // 根据openId查询superUser，并注入用户信息
        const superUser = await this.authService.findSuperUser(user.openId);
        if (!superUser) {
          throw new UnauthorizedException('未查询到当前账号相关监管单位，请联系管理员');
        }
        request.superUserInfo = {
          cname: superUser.cname, // 单位名称
          regAdd: superUser.regAdd, // 单位的管辖区域范围
          area_code: superUser.area_code, // 所在辖区区号 12位
          name: superUser.name, // 姓名
          _id: superUser._id,
        };
        console.log('🍊 superUserInfo=====', request.superUserInfo);
      }

      // 保持向后兼容
      request['tokenPayload'] = payload;

      return true;
    } catch (error) {
      throw new UnauthorizedException(
        error.response && error.response.message
          ? error.response.message
          : 'token验证失败，请重新登录',
      );
    }
  }
}

function extractTokenFromHeader(request) {
  if (!request.headers.authorization) {
    return null;
  }
  const token = request.headers.authorization.replace('Bearer ', '');
  return token;
}
