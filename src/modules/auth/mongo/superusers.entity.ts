import { Entity, ObjectIdColumn, Column, Index, CreateDateColumn, UpdateDateColumn } from 'typeorm';

class Member {
  @ObjectIdColumn()
  _id: string;

  @Column()
  name: string;

  @Column()
  nameForStore: string;

  @Column()
  nameSplitEncrypted: string;

  @Column()
  phoneNum: string;

  @Column()
  phoneNumForStore: string;

  @Column()
  phoneNumSplitEncrypted: string;

  @Column()
  userName: string;

  @Column()
  jobTitle: string;

  @Column()
  password: string;

  @Column()
  passwordEncryptionAlgorithm: string;

  @Column()
  landline: string;

  @Column('simple-array')
  roles: string[];

  @Column()
  group: string[];

  @Column()
  user_role_power_hmac: string;

  @Column()
  openId: string;
}

class HelpPersonnel {
  @Column()
  creatorSuperId: string;

  @Column()
  idNumber: string;
}

@Entity({ name: 'superusers' })
export class SuperUser {
  @ObjectIdColumn()
  _id: string;

  @Column({
    type: 'enum',
    enum: [1, 2, 3, 4],
    comment: '单位类型：1 卫生健康委 2 监督所 3 疾控中心 4 职防院'
  })
  type: number;

  @Column()
  cname: string;

  @Column('simple-array')
  regAdd: string[];

  @Column()
  @Index({ unique: true })
  area_code: string;

  @Column()
  @Index({ unique: true })
  orgCode: string;

  @Column()
  userName: string;

  @Column()
  name: string;

  @Column()
  nameForStore: string;

  @Column()
  nameSplitEncrypted: string;

  @Column()
  jobTitle: string;

  @Column()
  email: string;

  @Column()
  phoneNum: string;

  @Column()
  phoneNumForStore: string;

  @Column()
  phoneNumSplitEncrypted: string;

  @Column({ default: '86' })
  countryCode: string;

  @Column()
  password: string;

  @Column()
  passwordExpiresAt: Date;

  @Column({ default: 0 })
  loginAttempts: number;

  @Column('simple-array')
  loginAttemptsTimestamp: Date[];

  @Column()
  comments: string;

  @Column()
  landline: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({
    default: '/static/upload/images/defaultlogo.png'
  })
  logo: string;

  @Column({ default: false })
  enable: boolean;

  @Column({ default: '1' })
  state: string;

  @Column()
  group: string[];

  @Column(() => Member)
  members: Member[];

  @Column({ default: false })
  powerStatus: boolean;

  @Column()
  power: string[];

  @Column({ default: 'jg' })
  source: string;

  @Column()
  user_role_power_hmac: string;

  @Column()
  passwordEncryptionAlgorithm: string;

  @Column()
  user_role_power_hmac_algorithm: string;

  @Column()
  encryptionAlgorithm: string;

  @Column()
  openId: string;

  @Column(() => HelpPersonnel)
  helpPersonnels: HelpPersonnel[];

}