import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { AuthGuard } from './auth.guard';
import { UserModule } from '../user/user.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';
import { SuperUser } from './mongo/superusers.entity';

// 鉴权模块
@Module({
  imports: [
    UserModule,
    CacheModule.register(),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('jwtModule').secrect,
        signOptions: {
          expiresIn: configService.get('jwtModule').expiresIn, // token过期时效
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([SuperUser], 'mongodbConnection'),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    {
      provide: 'APP_GUARD', // 应用守卫
      useClass: AuthGuard,
    },
  ],
})
export class AuthModule {}
