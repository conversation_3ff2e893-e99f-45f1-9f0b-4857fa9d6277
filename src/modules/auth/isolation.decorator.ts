// 数据隔离 注入当前user和superUser信息
import { SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
export const IS_ISOLATION_KEY = 'isolation';
export const Isolation = () => SetMetadata(IS_ISOLATION_KEY, true);

export const userInfo = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.userInfo;
  },
);

export const superUserInfo = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.superUserInfo;
  },
);