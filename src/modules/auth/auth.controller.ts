import {
  Controller,
  Post,
  Body,
  HttpStatus,
  Req,
  Get,
  Query,
} from '@nestjs/common';
import { Public } from './public.decorator'; // 免鉴权
import { AuthService } from './auth.service';
import { success, error } from '../../utils/index';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) { }

  // 用户登录
  @Public()
  @Post('login')
  async login(@Body() body: { username: string, password: string }) {
    return this.authService
      .login(body.username, body.password)
      .then((res) => {
        // console.log('登录成功====', res);
        return success(res, '登录成功')
      })
      .catch((err) => {
        console.log('登录失败-====', err);
        return error(err.message || '登录失败', HttpStatus.UNAUTHORIZED);
      });
  }

  // 退出登录
  @Public()
  @Get('logout')
  async logout(@Req() req: any) {
    if (!req.user) {
      return error('找不到token, 请先登录再退出。', HttpStatus.UNAUTHORIZED);
    }
    return success(null, '退出登录成功');
  }


  // sso 登录
  @Public()
  @Get('ssoLogin')
  async ssoLogin(@Query() query: any) {
    console.log('统一门户登录=====', query);
    const { code } = query;
    if (!code) {
      return error('缺少code参数');
    }
    return this.authService
      .ssoLogin(code)
      .then((res) => success(res, '用户登录成功'))
      .catch((err) => error(err.message || '用户登录失败'));
  }

}
