import { Injectable, UnauthorizedException } from '@nestjs/common';
import * as md5 from 'md5';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository, Repository } from 'typeorm';
import { UserService } from '../user/user.service';
import { User } from '../user/entities/user.entity';
import { SuperUser } from './mongo/superusers.entity';


@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private jwtService: JwtService,
    private readonly configService: ConfigService,

    @InjectRepository(User, 'mysqlConnection')
    private readonly userRepository: Repository<User>,
    @InjectRepository(SuperUser, 'mongodbConnection')
    private readonly superuserRepository: MongoRepository<SuperUser>,
  ) { }

  private readonly sso = this.configService.get('sso') || '';
  // private readonly iServiceHost = this.configService.get('iServiceHost') || '';

  // 用户名密码登录
  async login(userName: string, password?: string) {
    try {
      const user = await this.userService.findByUserName(userName);
      // const md5Pssword = md5(password);
      if (user && user.password === password) {
        // jwt
        const paylaod = {
          userId: user.id,
          role: user.role,
        };
        const token = await this.jwtService.signAsync(paylaod)
        return { token };
      }
    }
    catch (error) {
      console.log(error);
    }
  }

  // sso 登录
  async ssoLogin(code: string) {
    try {
      const { httpUrl, clientId, clientSecret, redirect_uri } = this.sso;

      console.log('this.sso=====', this.sso);

      // 1、通过code获取token
      const payload = new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        code,
        redirect_uri,
        grant_type: 'authorization_code',
      });
      const res = await axios.post(
        `${httpUrl}/oauth2/token`,
        payload.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      console.log('获取token=====', res);
      const data = res.data;
      const { access_token } = data;

      // 2、通过token获取用户信息
      const res2 = await axios.post(
        `${httpUrl}/oauth2/userInfo`,
        new URLSearchParams({ access_token }).toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      console.log('获取用户信息=====', res2);
      const data2 = res2.data;
      if (data2.code !== '0') {
        throw new UnauthorizedException(`获取用户信息失败: ${data2.msg}`);
      }
      const userInfo = data2.data;
      if (!userInfo) {
        throw new UnauthorizedException(`获取用户信息失败2: ${data2.msg}`);
      }

      // 3、校验用户信息
      // 3.1、先查询用户是否存在
      let user = await this.userService.findByUserName(userInfo.userName);
      if (!user) {
        // 3.2、如果不存在，创建用户
        try {
          const user = new User();
          user.userName = userInfo.userName;
          user.password = '123456';
          user.name = userInfo.name;
          user.phoneNum = userInfo.phoneNum;
          user.orgname = userInfo.orgname;
          user.role = 'superAdmin';
          user.active = 1;
          user.openId = userInfo._id;
          await this.userRepository.save(user)
        } catch (error) {
          console.error('创建用户失败', error);
        }
      }
      // 4、登录成功，返回token
      const paylaod = {
        userId: user.id,
        role: user.role,
      };
      const token = await this.jwtService.signAsync(paylaod);
      return {
        // userInfo,
        token,
      };

    } catch (e) {
      throw new UnauthorizedException(e.message || '用户名或密码错误');
    }
  }

  async findSuperUser(openId: string) {

    return await this.superuserRepository.findOne({
      where: {
        $or: [
          { members: { $elemMatch: { openId: openId } } },
          { openId: openId },
        ]
      }
    });
  }

}
