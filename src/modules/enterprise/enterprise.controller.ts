import { Controller, Get, Param, Query } from '@nestjs/common';
import { EnterpriseService } from './enterprise.service';
import { wrapperResponse } from '../../utils/index';
import { Isolation, superUserInfo } from '../auth/isolation.decorator'; // 数据隔离

@Controller('enterprise')
export class EnterpriseController {
  constructor(private readonly enterpriseService: EnterpriseService) { }

  // 获取企业列表
  @Get()
  @Isolation()
  findAll(@Query() query: { name?: string }, @superUserInfo() superUserInfo: any) {
    return wrapperResponse(
      this.enterpriseService.findAll(query, superUserInfo),
      '获取企业列表成功',
    );
  }

  @Get(':id')
  findOne(@Param('id') _id: string) {
    // return this.enterpriseService.findOne(id);
    return wrapperResponse(
      this.enterpriseService.findOne(_id),
      '获取企业详情成功',
    );
  }

}
