import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EnterpriseService } from './enterprise.service';
import { EnterpriseController } from './enterprise.controller';
import { Adminorgs } from './mongo/adminorgs.entity';
import { Workspace } from '../employee/mongo/workspace.entity';
import { CheckAssessment } from './mongo/CheckAssessment.entity';
import { OccupationalExposureLimits } from './mongo/occupationalExposureLimits.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Adminorgs, Workspace, CheckAssessment, OccupationalExposureLimits], 'mongodbConnection',)],
  controllers: [EnterpriseController],
  providers: [EnterpriseService],
  exports: [EnterpriseService],
})
export class EnterpriseModule { }
