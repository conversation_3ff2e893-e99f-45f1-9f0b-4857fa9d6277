import { Entity, ObjectIdColumn, Column, BeforeInsert, Index } from 'typeorm';
import * as shortid from 'shortid';

/*
 * @Author: 黄婷婷
 * @Date: 2021-04-16 09:00
 * @LastEditors: 黄婷婷
 * @LastEditTime: 2021-04-16 09:00
 * @Description: 危害因素信息表
 *
 */

// 定点检测
class FixedPointDetection {
  @Column({ comment: '仪器', nullable: true })
  instrument?: string;

  @Column({ comment: '流量 总尘流量', nullable: true })
  flow?: string;

  @Column({ comment: '呼尘流量', nullable: true })
  respirableDust_flow?: string;

  @Column({ comment: '采样时间', nullable: true })
  samplingTime?: string;
}

// 个体检测
class IndividualDetection {
  @Column({ comment: '仪器', nullable: true })
  instrument?: string;

  @Column({ comment: '流量', nullable: true })
  flow?: string;

  @Column({ comment: '采样时间', nullable: true })
  samplingTime?: any;
}

// 标准信息
class StandardInfo {
  @Column({ comment: '标准名称', nullable: true })
  name?: string;

  @Column({ comment: '标准号', nullable: true })
  num?: string;
}

// 超高频辐射
class UltraHighRadiationTouch {
  @Column({ comment: '接触时间', nullable: true })
  touchTime?: string;

  @Column({ comment: '连续波 功率密度', nullable: true })
  seriesWavePowerDensity?: number;

  @Column({ comment: '连续波 电场强度', nullable: true })
  seriesWaveElectricIntensity?: number;

  @Column({ comment: '脉冲波 功率密度', nullable: true })
  pulseWavePowerDensity?: number;

  @Column({ comment: '脉冲波 电场强度', nullable: true })
  pulseWaveElectricIntensity?: number;
}

// 高频电磁场
class HighFrequencyElectromagnetic {
  @Column({ comment: '磁场强度', nullable: true })
  magneticIntensity?: string;

  @Column({ comment: '频率', nullable: true })
  frequency?: string;

  @Column({ comment: '电场强度', nullable: true })
  electricIntensity?: string;
}

// 工频电场
class PowerFrequencyElectric {
  @Column({ comment: '频率', nullable: true })
  frequency?: string;

  @Column({ comment: '电场强度', nullable: true })
  electricIntensity?: string;
}

// 激光辐射
class Laser {
  @Column({ comment: '光谱范围', nullable: true })
  spectralRange?: string;

  @Column({ comment: '波长', nullable: true })
  wavelength?: string;

  @Column({ comment: '照射时间', nullable: true })
  irradiationTime?: string;

  @Column({ comment: '照射量', nullable: true })
  irradiationDose?: string;

  @Column({ comment: '辐照度', nullable: true })
  irradiance?: string;
}

// 微波辐射
class Microwave {
  @Column({ comment: '类型', nullable: true })
  type?: string;

  @Column({ comment: '子类型', nullable: true })
  subType?: string;

  @Column({ comment: '日剂量', nullable: true })
  dailyDose?: string;

  @Column({ comment: '8h平均功率密度', nullable: true })
  averagePowerDensity?: string;

  @Column({ comment: '非8h平均功率密度', nullable: true })
  noAveragePowerDensity?: string;

  @Column({ comment: '短时间接触功率密度（mW/cm²）', nullable: true })
  shortTimeContactPowerDensity?: string;
}

// 紫外辐射
class UltravioletRadiation {
  @Column({ comment: '紫外光谱分类', nullable: true })
  category?: string;

  @Column({ comment: '辐照度', nullable: true })
  irradiance?: string;

  @Column({ comment: '辐射量', nullable: true })
  exposureDose?: string;
}

// 高温
class Heat {
  @Column({ comment: '接触时间率', nullable: true })
  contactTimeRate?: string;

  @Column({ comment: '体力劳动强度', nullable: true })
  laborIntensity?: string;

  @Column({ comment: '限值', nullable: true })
  touchLimit?: string;
}

// 体力劳动强度分级表
class LabourIntensityLevel {
  @Column({ comment: '体力劳动强度级别', nullable: true })
  level?: string;

  @Column({ comment: '劳动强度指数（n）', nullable: true })
  index?: string;
}

// 噪声
class Noise {
  @Column({ comment: '接触时间', nullable: true })
  touchTime?: string;

  @Column({ comment: '接触限值[dB(A)]', nullable: true })
  touchLimit?: string;

  @Column({ comment: '备注', nullable: true })
  note?: string;
}

// 手传振动
class HandBorneVibration {
  @Column({ comment: '接触时间', nullable: true })
  touchTime?: string;

  @Column({ comment: '等能量频率计权振动加速度（m/s²）', nullable: true })
  acceleration?: string;
}

@Entity('occupationalexposurelimits')
@Index(['initial'])
export class OccupationalExposureLimits {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '机构id', nullable: true })
  serviceOrgId?: string;

  @Column({ comment: '检测项目编号', nullable: true })
  projectCode?: string;

  @Column({ comment: '2.1标准名称', nullable: true })
  standardName2_1?: string;

  @Column({ comment: '2.1标准全名', nullable: true })
  standardName2_1FullName?: string;

  @Column({ comment: '是否有呼尘', nullable: true })
  hasRespirableDust?: boolean;

  @Column({ comment: '首字母', nullable: true })
  initial?: string;

  @Column({ comment: '搜索用的lht+ 拼音检索', type: 'array', default: [] })
  pinyin: any[];

  @Column({ comment: '别名', type: 'array', default: [] })
  chineseName: string[];

  @Column({ comment: '中文名', nullable: true })
  showName?: string;

  @Column({ comment: '英文名', nullable: true })
  englishName?: string;

  @Column({ comment: '危害因素分类', nullable: true })
  catetory?: string;

  @Column({ comment: '编码', nullable: true })
  code?: string;

  @Column({ comment: '化学文摘号', nullable: true })
  casNum?: string;

  @Column({ comment: 'MAC限值', nullable: true })
  MAC?: string;

  @Column({ comment: 'TWA限值 如果是粉尘的话是总尘', nullable: true })
  PC_TWA?: string;

  @Column({ comment: '粉尘-呼尘', nullable: true })
  respirableDust_TWA?: string;

  @Column({ comment: '是否需要做游离SiO2检测', default: false })
  FSiO2: boolean;

  @Column({ comment: 'STEL限值', nullable: true })
  PC_STEL?: string;

  @Column({ comment: 'PE限值', nullable: true })
  PE?: string;

  @Column({ comment: '呼尘的PE限值', nullable: true })
  respirableDust_PE?: string;

  @Column({ comment: '临界不良健康效应', nullable: true })
  healthEffect?: string;

  @Column({ comment: '备注', nullable: true })
  note?: string;

  @Column({ comment: '防护用品', nullable: true })
  protectiveEquipment?: string;

  @Column({ comment: '是否高毒 1表示高毒,0表示不是高毒', nullable: true })
  highHarm?: string;

  @Column({ comment: '是否是严重危害因素 1:严重 2:一般', nullable: true })
  seriousHarm?: string;

  @Column({ comment: '采样介质', nullable: true })
  samplingMedium?: string;

  @Column(() => FixedPointDetection)
  fixedPointDetection?: FixedPointDetection;

  @Column(() => IndividualDetection)
  individualDetection?: IndividualDetection;

  @Column({ comment: '储存时间', nullable: true })
  storageTime?: string;

  @Column({ comment: '标准 总尘', nullable: true })
  standard?: string;

  @Column(() => StandardInfo)
  standardInfo?: StandardInfo;

  @Column({ comment: '呼尘标准', nullable: true })
  respirableDust_standard?: string;

  @Column(() => StandardInfo)
  respirableDust_standardInfo?: StandardInfo;

  @Column({ comment: '物理别名', nullable: true })
  alias?: string;

  // ----------------物理因素限值信息----------------------------
  @Column(() => UltraHighRadiationTouch)
  ultraHighRadiationTouch: UltraHighRadiationTouch[];

  @Column(() => HighFrequencyElectromagnetic)
  highFrequencyElectromagnetic: HighFrequencyElectromagnetic[];

  @Column(() => PowerFrequencyElectric)
  powerFrequencyElectric: PowerFrequencyElectric[];

  @Column(() => Laser)
  laser: Laser[];

  @Column(() => Microwave)
  microwave: Microwave[];

  @Column(() => UltravioletRadiation)
  ultravioletRadiation: UltravioletRadiation[];

  @Column(() => Heat)
  heat: Heat[];

  @Column(() => LabourIntensityLevel)
  labourIntensityLevel: LabourIntensityLevel[];

  @Column(() => Noise)
  noise: Noise[];

  @Column(() => HandBorneVibration)
  handBorneVibration: HandBorneVibration[];

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}

