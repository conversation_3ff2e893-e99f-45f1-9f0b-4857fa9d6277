import { Logger } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { Adminorgs } from './mongo/adminorgs.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import * as moment from 'moment';
import { Workspace } from '../employee/mongo/workspace.entity';
import { CheckAssessment } from './mongo/CheckAssessment.entity';
import { OccupationalExposureLimits } from './mongo/occupationalExposureLimits.entity';

@Injectable()
export class EnterpriseService {
  private readonly logger = new Logger(EnterpriseService.name);

  constructor(
    @InjectRepository(Adminorgs, 'mongodbConnection')
    private readonly AdminorgsRepository: MongoRepository<Adminorgs>,
    @InjectRepository(Workspace, 'mongodbConnection')
    private readonly WorkspaceRepository: MongoRepository<Workspace>,
    @InjectRepository(CheckAssessment, 'mongodbConnection')
    private readonly CheckAssessmentRepository: MongoRepository<CheckAssessment>,
    @InjectRepository(OccupationalExposureLimits, 'mongodbConnection')
    private readonly OccupationalExposureLimitsRepository: MongoRepository<OccupationalExposureLimits>
  ) { }

  async findAll(query: any, superUserInfo: any) {
    const { name, pageNum = 1, pageSize = 10, timePoint, timeRange, district, industryCategory, harm } = query;
    const queryParams = {};

    // 如果有危害因素分类，先查询有对应危害因素分类的危害因素的工作场所，过滤出包含这些工作场所的企业id
    if (harm) {
      // 获取当前危害因素分类的所有危害因素名称
      let names = await this.OccupationalExposureLimitsRepository.aggregate([
        { $match: { catetory: harm } },
        { $unwind: '$chineseName' },
        { $group: { _id: '$catetory', harmFactors: { $push: { label: '$chineseName' } } } },
        { $project: { label: '$_id', harmFactors: 1 } },
      ]).toArray() as any;
      names = names[0].harmFactors.map((item: any) => item.label);
      // 查询有对应危害因素的工作场所
      const res = await this.WorkspaceRepository.aggregate([
        {
          $match: {
            'stations.harmFactors': { $in: names },
          },
        },
        {
          $project: {
            _id: 1,
            EnterpriseID: 1,
          },
        },
      ]).toArray() as any[];
      // 过滤出id并去重
      const EnterpriseIDs = res.map((item: any) => item.EnterpriseID);
      const uniqueEnterpriseIDs = [...new Set(EnterpriseIDs)];
      // 将企业id添加到查询参数中
      queryParams['_id'] = { $in: uniqueEnterpriseIDs };
    }

    if (name) {
      queryParams['cname'] = { $regex: name };
    }
    if (timePoint) {
      queryParams['createTime'] = { $gte: moment(timePoint).startOf('day').toDate(), $lte: moment(timePoint).endOf('day').toDate() };
    }
    if (timeRange) {
      const [start, end] = timeRange.split(',');
      queryParams['createTime'] = { $gte: moment(start).startOf('day').toDate(), $lte: moment(end).endOf('day').toDate() };
    }
    if (superUserInfo) {
      queryParams['districtRegAdd'] = { $all: superUserInfo.regAdd };
    }
    // district是数组
    if (district) {
      queryParams['districtRegAdd'] = { $all: query.district.split(',') };
    }
    const match2 = {}
    if (industryCategory) {
      if (query.industryCategory.length === 1) {
        match2['industryCategory'] = { $all: [query.industryCategory] };
      } else {
        match2['industryCategory'] = { $all: query.industryCategory.split(',') };
      }
    }
    console.log('queryParams', queryParams);
    const pipeline: any = [
      { $match: queryParams },
      // 将industryCategory字段的值使用industryCategory.0替换,其他字段不变
      {
        $addFields: {
          industryCategory: {
            $cond: {
              if: { $gt: [{ $size: '$industryCategory' }, 0] },
              then: { $arrayElemAt: ['$industryCategory', 0] },
              else: null
            }
          }
        }
      },
    ];
    if (industryCategory) {
      pipeline.push({ $match: match2 });
    }
    pipeline.push(
      {
        $sort: { createdAt: -1 },
      },
      {
        $facet: {
          list: [
            { $skip: (pageNum - 1) * pageSize },
            { $limit: +pageSize },

          ],
          total: [
            { $count: 'total' }
          ]
        }
      },
      {
        $project: {
          list: 1,
          total: { $arrayElemAt: ['$total.total', 0] }
        }
      }
    )

    const list = await this.AdminorgsRepository.aggregate(pipeline).toArray() as any[];
    let res = null
    if (list.length === 0) {
      res = { list: [], total: 0 };
    }
    if (list.length > 0) {
      res = list[0];
      if (!res.total) {
        res.total = 0;
      }
    }
    return res;
  }

  async findOne(_id: string) {
    // return await this.AdminorgsRepository.findOne({ where: { _id } });
    // 改为聚合查询
    const pipeline = [
      { $match: { _id } },
      {
        $lookup: {
          from: 'adminusers',
          localField: 'adminUserId',
          foreignField: '_id',
          as: 'adminUser',
        }
      },
      {
        $unwind: '$adminUser'
      },
      // 将industryCategory字段的值使用industryCategory.0替换,其他字段不变
      {
        $addFields: {
          industryCategory: {
            $cond: {
              if: { $gt: [{ $size: '$industryCategory' }, 0] },
              then: { $arrayElemAt: ['$industryCategory', 0] },
              else: null
            }
          }
        }
      },
    ];
    const list = await this.AdminorgsRepository.aggregate(pipeline).toArray();

    const res = list[0] as any;

    const EnterpriseID = res._id;

    const workshopList = await this.getWorkshopList(EnterpriseID);
    // res.workshopList = workshopList;
    // console.log('workshopList', workshopList);
    const workshopNameArr = workshopList.map((item: any) => item.name);
    const statistics = await this.getWorkspaceStatistics(EnterpriseID, workshopNameArr);
    res.statistics = statistics;

    return res;
  }

  // 获取工作场所一级(顶级)数据列表
  async getWorkshopList(EnterpriseID: string) {
    const list = await this.WorkspaceRepository.find({
      where: { EnterpriseID },
      select: ['workshopName', 'workspaceName', 'employees', 'status']
    });
    const result = {};
    for (let i = 0; i < list.length; i++) {
      const { workshopName, workspaceName, _id } = list[i];
      const key = workshopName || workspaceName;
      if (!result[key]) result[key] = { ids: [], employeeCount: 0, status: '1', type: workshopName ? 'workshop' : 'workspace' };
      result[key].ids.push(_id);
      result[key].employeeCount += list[i].employees.length;
      if (list[i].status === '0') result[key].status = '0';
    }
    return Object.entries(result).map(([name, statics]: [string, { ids: any[]; employeeCount: number; status: string; type: string }]) => {
      return {
        name,
        ids: statics.ids,
        employeeCount: statics.employeeCount,
        status: statics.status,
        type: statics.type,
      };
    });
  }

  // 工作场所统计:接害人数统计，危害因素一览（车间岗位危害因素和人员数量） workshopNameArr = ['厂房1', '车间2']
  async getWorkspaceStatistics(EnterpriseID: string, workshopNameArr = [],) {
    const query = {
      EnterpriseID,
      $or: [
        { workshopName: { $in: workshopNameArr } },
        { workspaceName: { $in: workshopNameArr } },
      ],
    };
    // 接害人数统计
    let stationStatistics = await this.WorkspaceRepository.aggregate([
      {
        $match: query,
      },
      {
        $project: {
          _id: 1,
          workshopName: 1,
          workspaceName: 1,
          workTypeName: 1,
          employeeCount: { $size: { $ifNull: ['$employees', []] } },
          harmFactors: {
            $reduce: {
              input: { $ifNull: ['$stations.harmFactors', []] },
              initialValue: [],
              in: { $concatArrays: ['$$value', '$$this'] },
            },
          },
        },
      },
    ]).toArray();
    stationStatistics = JSON.parse(JSON.stringify(stationStatistics));
    for (let i = 0; i < stationStatistics.length; i++) {
      (stationStatistics[i] as any).harmFactors = [...new Set((stationStatistics[i] as any).harmFactors)];
    }

    // 获取每个车间/厂房的危害因素一览
    let harmStatistics = await this.WorkspaceRepository.aggregate([
      {
        $match: query,
      },
      {
        $project: {
          workshopName: 1,
          workspaceName: 1,
          employeeCount: { $size: { $ifNull: ['$employees', []] } },
          harmFactors: {
            $reduce: {
              input: { $ifNull: ['$stations.harmFactors', []] },
              initialValue: [],
              in: { $concatArrays: ['$$value', '$$this'] },
            },
          },
        },
      },
      {
        $group: {
          _id: { workshopName: '$workshopName', workspaceName: '$workspaceName' },
          totalEmployeeCount: { $sum: '$employeeCount' },
          harmFactors: { $push: '$harmFactors' },
        },
      },
      {
        $project: {
          _id: 0,
          workshopName: '$_id.workshopName',
          workspaceName: '$_id.workspaceName',
          totalEmployeeCount: 1,
          harmFactors: {
            $reduce: {
              input: '$harmFactors',
              initialValue: [],
              in: { $concatArrays: ['$$value', '$$this'] },
            },
          },
        },
      },
    ]).toArray() as unknown as Array<{
      workshopName: string;
      workspaceName: string;
      totalEmployeeCount: number;
      harmFactors: string[];
      harmFactorsStatics?: any;
    }>;
    harmStatistics = JSON.parse(JSON.stringify(harmStatistics));
    // 危害因素类别
    let categoriesResult = await this.OccupationalExposureLimitsRepository.aggregate([
      {
        $group: {
          _id: null,
          categories: { $addToSet: '$catetory' },
        },
      },
    ]).toArray() as unknown as Array<{ _id: null; categories: string[] }>;
    const categories = categoriesResult.length > 0 ? categoriesResult[0].categories : [];
    // 统计各种危害因素类型的数量
    for (let i = 0; i < harmStatistics.length; i++) {
      harmStatistics[i].harmFactors = [...new Set(harmStatistics[i].harmFactors)];
      const { harmFactors } = harmStatistics[i];
      const statics = {
        seriousHarm: 0,
        notSeriousHarm: 0,
      };
      categories.forEach(item => {
        statics[item] = 0;
      });
      for (let j = 0; j < harmFactors.length; j++) {
        const harmFactor = harmFactors[j];
        const occupationalexposure = await this.OccupationalExposureLimitsRepository.findOne({
          where: { chineseName: harmFactor }
        });
        if (occupationalexposure) {
          if (occupationalexposure.seriousHarm === '1') {
            statics.seriousHarm++;
          } else {
            statics.notSeriousHarm++;
          }
          if (!statics[occupationalexposure.catetory]) statics[occupationalexposure.catetory] = 0;
          statics[occupationalexposure.catetory]++;
        } else {
          this.logger.log(harmFactor + '在OccupationalexposureLimits中找不到');
        }
      }
      harmStatistics[i].harmFactorsStatics = statics;
    }
    return { stationStatistics, categories, harmStatistics };
  }


  // #region tool

  /**
   * 获取所有危害因素
   * @returns 
   */
  async findAllHarmFactors(): Promise<OccupationalExposureLimits[]> {
    try {
      const res = await this.OccupationalExposureLimitsRepository.aggregate([
        { $unwind: '$chineseName' },
        { $group: { _id: '$catetory', harmFactors: { $push: { label: '$chineseName' } } } },
        { $project: { label: '$_id', harmFactors: 1 } },
      ]).toArray();
      return res;
    } catch (error) {
      this.logger.error(
        `获取所有危害因素失败: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }
}
