import {
  Controller,
  Get,
  Param,
  HttpException,
  HttpStatus,
  Body,
  Post,
  Query,
  Delete,
  Req,
  Put,
  UseInterceptors,
  UploadedFile,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ToolService } from './tool.service';
import { wrapperResponse } from '../../utils/index';
import { storage } from 'src/utils/storage'; // 存储引擎
import { Public } from '../auth/public.decorator';
import { Roles } from '../auth/roles.decorator';
import { CreateUserDto, UpdateUserDto } from './tool.dto';

@Controller('tool')
export class ToolController {
  constructor(private readonly ToolService: ToolService) { }

  @Public()
  @Get('/district')
  async getDistrict(@Query() query: { level?: string, parent_code?: string }) {
    if (Object.keys(query).length === 0) {
      query = { level: '0' }
    }
    return wrapperResponse(
      this.ToolService.findDistricts(query),
      '获取地区列表成功',
    );
  }

  @Public()
  @Get('/industryCategory')
  async getIndustryCategory() {
    return wrapperResponse(
      this.ToolService.findIndustryCategory(),
      '获取行业分类列表成功',
    );
  }

}
