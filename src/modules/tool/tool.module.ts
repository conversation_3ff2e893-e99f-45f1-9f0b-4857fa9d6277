import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ToolController } from './tool.controller';
import { ToolService } from './tool.service';
import { District } from './mongo/district.entity';
import { IndustryCategory } from './mongo/industryCategory.entity';

// 用户管理模块
@Module({
  imports: [TypeOrmModule.forFeature([
    District,
    IndustryCategory
  ],
    'mongodbConnection',
  )],
  controllers: [ToolController],
  providers: [ToolService],
  exports: [ToolService],
})
export class ToolModule { }
