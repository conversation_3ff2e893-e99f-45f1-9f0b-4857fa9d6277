import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { District } from './mongo/district.entity';
import { IndustryCategory } from './mongo/industryCategory.entity';
// import { CreateUserDto, UpdateUserDto } from './tool.dto';
// import { timeFormatter } from '../../utils/index';
// import { ConfigService } from '@nestjs/config';
@Injectable()
export class ToolService {
  constructor(
    @InjectRepository(District, 'mongodbConnection')
    private districtRepository: MongoRepository<District>,
    @InjectRepository(IndustryCategory, 'mongodbConnection')
    private industryCategoryRepository: MongoRepository<IndustryCategory>,
  ) { }

  async findDistricts(query: { level?: string, parent_code?: string }): Promise<District[]> {
    const res = await this.districtRepository.find({
      where: query,
    });
    return res;
  }

  async findIndustryCategory(): Promise<IndustryCategory[]> {
    const res = await this.industryCategoryRepository.find();
    return res;
  }

}
