import { Entity, Column, ObjectIdColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

class FixedPointDetection {
  @Column()
  instrument: string; // 仪器

  @Column()
  flow: string; // 流量 总尘流量

  @Column()
  respirableDust_flow: string; // 呼尘流量

  @Column()
  samplingTime: string; // 采样时间
}

class IndividualDetection {
  @Column()
  instrument: string; // 仪器

  @Column()
  flow: string; // 流量

  @Column()
  samplingTime: string; // 采样时间
}

class StandardInfo {
  @Column()
  name: string; // 标准名称

  @Column()
  num: string; // 标准号
}

class UltraHighRadiationTouch {
  @ObjectIdColumn()
  _id: string;

  @Column()
  touchTime: string; // 接触时间

  @Column()
  seriesWavePowerDensity: number; // 连续波 功率密度

  @Column()
  seriesWaveElectricIntensity: number; // 连续波 电场强度

  @Column()
  pulseWavePowerDensity: number; // 脉冲波 功率密度

  @Column()
  pulseWaveElectricIntensity: number; // 脉冲波 电场强度
}

class HighFrequencyElectromagnetic {
  @ObjectIdColumn()
  _id: string;

  @Column()
  magneticIntensity: string; // 磁场强度

  @Column()
  frequency: string; // 频率

  @Column()
  electricIntensity: string; // 电场强度
}

class PowerFrequencyElectric {
  @ObjectIdColumn()
  _id: string;

  @Column()
  frequency: string; // 频率

  @Column()
  electricIntensity: string; // 电场强度
}

class Laser {
  @ObjectIdColumn()
  _id: string;

  @Column()
  spectralRange: string; // 光谱范围

  @Column()
  wavelength: string; // 波长

  @Column()
  irradiationTime: string; // 照射时间

  @Column()
  irradiationDose: string; // 照射量

  @Column()
  irradiance: string; // 辐照度
}

class Microwave {
  @ObjectIdColumn()
  _id: string;

  @Column()
  type: string; // 类型

  @Column()
  subType: string; // 子类型

  @Column()
  dailyDose: string; // 日剂量

  @Column()
  averagePowerDensity: string; // 8h平均功率密度

  @Column()
  noAveragePowerDensity: string; // 非8h平均功率密度

  @Column()
  shortTimeContactPowerDensity: string; // 短时间接触功率密度（mW/cm²）
}

class UltravioletRadiation {
  @ObjectIdColumn()
  _id: string;

  @Column()
  category: string; // 紫外光谱分类

  @Column()
  irradiance: string; // 辐照度

  @Column()
  exposureDose: string; // 辐射量
}

class Heat {
  @ObjectIdColumn()
  _id: string;

  @Column()
  contactTimeRate: string; // 接触时间率

  @Column()
  laborIntensity: string; // 体力劳动强度

  @Column()
  touchLimit: string; // 限值
}

class LabourIntensityLevel {
  @ObjectIdColumn()
  _id: string;

  @Column()
  level: string; // 体力劳动强度级别

  @Column()
  index: string; // 劳动强度指数（n）
}

class Noise {
  @ObjectIdColumn()
  _id: string;

  @Column()
  touchTime: string; // 接触时间

  @Column()
  touchLimit: string; // 接触限值[dB(A)]

  @Column()
  note: string; // 备注
}

class HandBorneVibration {
  @ObjectIdColumn()
  _id: string;

  @Column()
  touchTime: string; // 接触时间

  @Column()
  acceleration: string; // 等能量频率计权振动加速度（m/s²）
}

@Entity('occupationalexposurelimits')
export class OccupationalExposureLimits {
  @ObjectIdColumn()
  _id: string;

  @Column()
  serviceOrgId: string; // 机构id

  @Column()
  projectCode: string; // 检测项目编号

  @Column()
  standardName2_1: string; // 2.1标准名称

  @Column()
  standardName2_1FullName: string; // 2.1标准全名

  @Column()
  hasRespirableDust: boolean; // 是否有呼尘

  @Column()
  decimal: number; // 小数点位数

  @Column()
  RespirableDustDecimal: number; // 呼尘小数点位数

  @Column()
  initial: string; // 首字母

  @Column()
  RCId: string; // 汝成危害因素id

  @Column("simple-array")
  pinyin: string[]; // 搜索用的lht+

  @Column("simple-array")
  chineseName: string[]; // 中文名

  @Column()
  showName: string; // 展示的名字

  @Column()
  englishName: string; // 英文名

  @Column()
  catetory: string; // 危害因素分类

  @Column()
  casNum: string; // 化学文摘号

  @Column()
  MAC: string; // MAC限值

  @Column()
  PC_TWA: string; // TWA限值 如果是粉尘的话是总尘

  @Column()
  respirableDust_TWA: string; // 粉尘-呼尘

  @Column()
  FSiO2: boolean; // 是否需要做游离SiO2检测

  @Column()
  PC_STEL: string; // STEL限值

  @Column()
  PE: string; // PE限值

  @Column()
  respirableDust_PE: string; // 呼尘的PE限值

  @Column()
  healthEffect: string; // 临界不良健康效应

  @Column()
  invasionPathway: string; // 侵入途径

  @Column()
  disease: string; // 法定职业病

  @Column()
  note: string; // 备注

  @Column()
  protectiveEquipment: string; // 防护用品

  @Column()
  highHarm: string; // 是否高毒 '1'表示高毒,‘0’表示不是高毒

  @Column()
  seriousHarm: string; // 是否是严重危害因素 '1':严重 '2':一般

  @Column()
  samplingMedium: string; // 采样介质

  @Column()
  collector: string; // 收集器

  @Column(type => FixedPointDetection)
  fixedPointDetection: FixedPointDetection; // 定点检测

  @Column(type => IndividualDetection)
  individualDetection: IndividualDetection; // 个体检测

  @Column()
  storageTime: string; // 储存时间

  @Column()
  standard: string; // 标准 总尘

  @Column(type => StandardInfo)
  standardInfo: StandardInfo; // 标准信息

  @Column()
  respirableDust_standard: string; // 呼尘标准

  @Column(type => StandardInfo)
  respirableDust_standardInfo: StandardInfo; // 呼尘标准信息

  @Column()
  QualityControlSampleId: string; // 质控样

  @Column()
  StandardSolutionsId: string; // 标准液

  @Column()
  alias: string; // 别名

  @Column(type => UltraHighRadiationTouch)
  ultraHighRadiationTouch: UltraHighRadiationTouch[]; // 超高频辐射

  @Column(type => HighFrequencyElectromagnetic)
  highFrequencyElectromagnetic: HighFrequencyElectromagnetic[]; // 高频电磁场

  @Column(type => PowerFrequencyElectric)
  powerFrequencyElectric: PowerFrequencyElectric[]; // 工频电场

  @Column(type => Laser)
  laser: Laser[]; // 激光辐射

  @Column(type => Microwave)
  microwave: Microwave[]; // 微波辐射

  @Column(type => UltravioletRadiation)
  ultravioletRadiation: UltravioletRadiation[]; // 紫外辐射

  @Column(type => Heat)
  heat: Heat[]; // 高温

  @Column(type => LabourIntensityLevel)
  labourIntensityLevel: LabourIntensityLevel[]; // 体力劳动强度分级表

  @Column(type => Noise)
  noise: Noise[]; // 噪声

  @Column(type => HandBorneVibration)
  handBorneVibration: HandBorneVibration[]; // 手传振动

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}