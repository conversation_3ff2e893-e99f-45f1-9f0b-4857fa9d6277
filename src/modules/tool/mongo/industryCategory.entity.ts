import { Entity, Column, ObjectIdColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

@Entity('industryCategory')
export class IndustryCategory {
  @ObjectIdColumn()
  _id: string;

  @Column()
  value: string; // 编号

  @Column()
  label: string; // 名字

  @Column()
  sort: number; // 排序

  @Column({ default: 0 })
  isJialei: number; // 是否甲类

  @Column({ default: 0 })
  riskType: number; // 职业病危害风险分类,分为：2:严重 ;1:较重 ; 0:一般

  @Column({ type: 'json', default: [] })
  children: IndustryCategory[]; // 下级

  @BeforeInsert()
  setDefaultId() {
    if (!this._id) {
      this._id = shortid.generate();
    }
  }
}