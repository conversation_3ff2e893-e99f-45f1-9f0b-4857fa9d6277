import { Entity, Column, ObjectIdColumn, Index, BeforeInsert } from 'typeorm';
// import * as shortid from 'shortid';

@Entity('district')
  export class District {
    @ObjectIdColumn()
    _id: string;

    @Column()
    id: string;

    @Column()
    parent_code: string;

    @Column()
    name: string;

    @Column()
    merger_name: string;

    @Column()
    area_code: string;

    @Column()
    city_code: string;

    @Column()
    lat: string;

    @Column()
    lng: string;

    @Column()
    level: string;

    @Column()
    short_name: string;

    @Column()
    pinyin: string;

    @Column()
    zip_code: string;

    // @BeforeInsert()
    // generateId() {
    //   this._id = shortid.generate();
    // }
  }
