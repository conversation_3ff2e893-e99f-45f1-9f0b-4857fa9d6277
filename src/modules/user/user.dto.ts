import {
  IsNotEmpty,
  IsEnum,
  IsOptional,
  // IsNumber,
  IsString,
  IsPhoneNumber,
} from 'class-validator';
export class CreateUserDto {
  @IsOptional()
  @IsString()
  userName?: string;

  @IsOptional()
  password?: string;

  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  @IsPhoneNumber('CN', { message: '222手机号格式不正确' })
  phoneNum: string;

  @IsEnum([ 'admin', 'user' ])
  role: string;

  @IsOptional()
  enterpriseId?: string;

  @IsOptional()
  active?: number;

  @IsOptional()
  avatar?: string;

  createdAt?: Date;
  updatedAt?: Date;
}

export class UpdateUserDto {
  @IsString()
  id: number;

  @IsOptional()
  @IsNotEmpty()
  userName?: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsNotEmpty()
  name?: string;

  @IsOptional()
  @IsNotEmpty()
  @IsPhoneNumber('CN', { message: '手机号格式不正确' })
  phoneNum?: string;

  @IsOptional()
  @IsNotEmpty()
  @IsEnum([
    'superAdmin',
    'admin',
    'user',
  ])
  role?: string;

  @IsOptional()
  @IsNotEmpty()
  @IsString()
  enterpriseId?: string;

  @IsOptional()
  @IsNotEmpty()
  active?: number;

  @IsOptional()
  @IsNotEmpty()
  avatar?: string;

  createdAt?: Date;
  updatedAt?: Date;
}
