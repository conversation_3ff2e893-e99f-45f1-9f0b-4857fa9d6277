import {
  Controller,
  Get,
  Param,
  HttpException,
  HttpStatus,
  Body,
  Post,
  Query,
  Delete,
  Req,
  Put,
  UseInterceptors,
  UploadedFile,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { UserService } from './user.service';
import { wrapperResponse } from '../../utils/index';
import { FileInterceptor } from '@nestjs/platform-express';
import { storage } from 'src/utils/storage'; // 存储引擎
import { Public } from '../auth/public.decorator';
import { Roles } from '../auth/roles.decorator';
import { CreateUserDto, UpdateUserDto } from './user.dto';

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Public()
  @Get('/test')
  async test() {
    return wrapperResponse(
      this.userService.findOneById(1),
      '获取用户列表成功',
    );
  }

  // 获取用户列表
  @Roles('superAdmin', 'admin' )
  @Get()
  async getUserList(@Req() req, @Query() query) {
    const currentUser = req.user;
    return wrapperResponse(
      this.userService.list(query, currentUser),
      '获取用户列表成功',
    );
  }
  // 创建新用户
  @Roles('superAdmin', 'admin')
  @Post()
  @UsePipes(new ValidationPipe())
  async createUser(@Req() req, @Body() body: CreateUserDto) {
    const currentUser = req.user;
    body.active = body.active == 0 ? 0 : 1;
    return wrapperResponse(
      this.userService.create(body, currentUser),
      '创建用户成功',
    );
  }
  // 彻底删除用户
  @Roles('superAdmin', 'admin')
  @Delete('/:id')
  async removeUser(@Req() req, @Param('id') id: number) {
    if (!id) {
      throw new HttpException('id是必传参', HttpStatus.BAD_REQUEST);
    }
    const currentUser = req.user;
    if (currentUser.userId === id) {
      throw new HttpException('不能删除自己', HttpStatus.BAD_REQUEST);
    }
    return wrapperResponse(
      this.userService.remove(id, currentUser),
      '删除用户成功',
    );
  }
  // 根据token获取当前登录用户信息
  @Get('info')
  async getUserByToken(@Req() req) {
    if (!req.user) {
      throw new HttpException('请先登录', HttpStatus.UNAUTHORIZED);
    }
    return wrapperResponse(
      this.userService.findOneById(req.user.userId || req.user._id),
      '获取用户信息成功',
    );
  }
  // 根据id获取用户详情
  @Get(':id')
  async getUserInfo(@Param('id') id: number) {
    if (!id) {
      throw new HttpException('id是必传参', HttpStatus.BAD_REQUEST);
    }
    return wrapperResponse(
      this.userService.findOneById(id),
      '获取用户详情成功',
    );
  }
  // 更新用户信息及头像
  @Public()
  @Put()
  @UsePipes(new ValidationPipe())
  @UseInterceptors(FileInterceptor('avatar', { storage }))
  async updateUser(
    @Req() req,
    @UploadedFile() file,
    @Body() body: UpdateUserDto,
  ) {
    const { id } = body;
    if (!id) {
      throw new HttpException('_id是必传参', HttpStatus.BAD_REQUEST);
    }
    if (file && file.filename) {
      body.avatar = file.filename;
      const user = await this.userService.findOneById(id);
      // if (user && user.avatar) {
      //   this.userService.removeAvatar(user.avatar);
      // }
    } else {
      delete body.avatar;
    }
    delete body.id;
    if (typeof body.active === 'string') {
      body.active = +body.active;
    }
    const currentUser = req.user; // 这个没有，因为是表单提交，没有token
    return wrapperResponse(
      this.userService.update(id, body, currentUser),
      '更新用户信息成功',
    );
  }
}
