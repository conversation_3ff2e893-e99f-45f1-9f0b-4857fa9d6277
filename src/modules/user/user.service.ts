import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto, UpdateUserDto } from './user.dto';
import * as md5 from 'md5';
import { timeFormatter } from '../../utils/index';
import * as fs from 'fs';
import * as path from 'path';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User, 'mysqlConnection')
    private readonly userRepository: Repository<User>, // user entity的实例
    private readonly configService: ConfigService,
  ) { }
  private readonly uploadPathUrl =
    this.configService.get('uploadPathUrl') || '';

  // 查询单个用户详情
  async findOneById(id: number): Promise<User> {
    const res = await this.userRepository.findOneBy({ id });
    if (!res) {
      throw new HttpException('用户_id不存在', HttpStatus.BAD_REQUEST);
    }
    // if (res.avatar) {
    //   res.avatar = `${this.uploadPathUrl}/${res.avatar}`;
    // }
    delete res.password;
    return res;
  }
  // 用户名密码登录时查询用户及密码
  async findByUserName(userName: string): Promise<User> {
      const listAll = await this.userRepository.find({});
      if (listAll.length === 0) {
        // 创建新用户 admin/123456
        const user = new User();
        user.userName = 'admin';
        user.password = '123456';
        user.name = 'admin';
        user.phoneNum = '18079514443';
        user.role = 'superAdmin';
        user.active = 1;
        user.openId = 'IaMNFvTKW';
        await this.userRepository.save(user)
      }
      const list = await this.userRepository.find({ where: { userName } });
      if (list.length === 0) {
        return null;
      } else if (list.length > 1) {
        throw new HttpException('此用户名有多个账号', HttpStatus.BAD_REQUEST);
      }
      const res = list[0];
      // if (res.avatar) {
      //   res.avatar = `${this.uploadPathUrl}/${res.avatar}`;
      // }
      return res;

  }
  // 手机验证码登录时查询用户
  async findByPhoneNum(phoneNum: string): Promise<User> {
    const res = await this.userRepository.find({ where: { phoneNum } });
    if (res.length === 0) {
      return null;
    } else if (res.length > 1) {
      throw new HttpException('手机号重复', HttpStatus.BAD_REQUEST);
    }
    return res[0];
  }

  // 按条件查询所有用户
  async find(query: any) {
    query.active = query.active || 1;
    const res = await this.userRepository.find(query);
    return res;
  }

  // 获取用户列表
  async list(query, currentUser): Promise<object> {
    const page = +query.page || 1,
      pageSize = +query.pageSize || 10;
    const where = {};
    if (query.active) {
      where['active'] = +query.active;
    } else if (query.active == 0) {
      where['active'] = 0;
    }
    if (query.keyWord) {
      const reg = new RegExp(query.keyWord, 'i');
      where['$or'] = [{ userName: reg }, { name: reg }, { phoneNum: reg }];
    }
    if (['superAdmin', 'admin'].includes(currentUser.role)) {
      if (query.roles && Array.isArray(query.roles)) {
        where['role'] = { $in: query.roles };
      } else {
        throw new HttpException('请传参数roles', HttpStatus.BAD_REQUEST);
      }
    }
    // else {
    // const userInfo = await this.findOneById(currentUser.userId);
    // where['enterpriseId'] = userInfo.enterpriseId;
    // }
    // if (query.enterpriseId) where['enterpriseId'] = query.enterpriseId;
    const [users, count] = await this.userRepository.findAndCount({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      order: { createdAt: 'DESC' },
      select: {
        password: false,
      },
    });
    const list = timeFormatter(users);
    return { count, list };
  }
  // 创建新用户
  async create(createUserDto: CreateUserDto, currentUser: any): Promise<User> {
    if (!createUserDto.userName) {
      createUserDto.userName = createUserDto.phoneNum;
    }
    const existingUser = await this.userRepository.count({
      where: { userName: createUserDto.userName },
    });
    if (existingUser) {
      throw new HttpException('用户名已存在', HttpStatus.BAD_REQUEST);
    }
    const existingPhoneNum = await this.userRepository.count({
      where: { phoneNum: createUserDto.phoneNum },
    });
    if (existingPhoneNum) {
      throw new HttpException('手机号已存在', HttpStatus.BAD_REQUEST);
    }
    if (currentUser) {
      const checkRole = await this.checkRole(currentUser, createUserDto.role);
      if (!checkRole) {
        throw new HttpException(
          createUserDto.role + '用户角色创建权限不够',
          HttpStatus.BAD_REQUEST,
        );
      }
    }

    const user = new User();
    user.userName = createUserDto.userName;
    // if (createUserDto.password) user.password = md5(createUserDto.password);
    if (createUserDto.password) user.password = createUserDto.password;
    user.name = createUserDto.name || '';
    user.phoneNum = createUserDto.phoneNum || '';
    user.role = createUserDto.role;
    user.active = createUserDto.active == 0 ? 0 : 1;
    // if (createUserDto.avatar) user.avatar = createUserDto.avatar;
    // user.enterpriseId = createUserDto.enterpriseId || '';
    return await this.userRepository.save(user);
  }

  async remove(id: number, currentUser: any): Promise<void> {
    const user = await this.findOneById(id);
    const checkRole = await this.checkRole(currentUser, user.role);
    if (!checkRole) {
      throw new HttpException('用户角色删除权限不够', HttpStatus.BAD_REQUEST);
    }
    await this.userRepository.delete(id);
  }

  // 更新用户信息
  async update(
    id: number,
    updateUserDto: UpdateUserDto,
    currentUser: any,
  ): Promise<void> {
    const user = await this.findOneById(id);
    if (!user) {
      throw new HttpException('用户_id不存在', HttpStatus.BAD_REQUEST);
    }
    if (currentUser && updateUserDto.role && updateUserDto.role !== user.role) {
      const checkRole = await this.checkRole(currentUser, updateUserDto.role);
      if (!checkRole) {
        throw new HttpException('用户角色传参错误', HttpStatus.BAD_REQUEST);
      }
    }
    if (updateUserDto.userName && user.userName !== updateUserDto.userName) {
      const existingUser = await this.userRepository.count({
        where: {
          userName: updateUserDto.userName,
          id: Not(id),
        },
      });
      if (existingUser) {
        throw new HttpException('用户名已存在', HttpStatus.BAD_REQUEST);
      }
    } else {
      delete updateUserDto.userName;
    }
    if (updateUserDto.phoneNum && user.phoneNum !== updateUserDto.phoneNum) {
      const existingPhoneNum = await this.userRepository.count({
        where: {
          phoneNum: updateUserDto.phoneNum,
          id: Not(id),
        },
      });
      if (existingPhoneNum) {
        throw new HttpException('手机号已存在', HttpStatus.BAD_REQUEST);
      }
    }
    if (
      updateUserDto.password &&
      // user.password !== md5(updateUserDto.password)
      user.password !== updateUserDto.password
    ) {
      // 修改个人密码
      if (updateUserDto.password.length < 6) {
        throw new HttpException('密码长度不能小于6位', HttpStatus.BAD_REQUEST);
      } else if (updateUserDto.password.length < 20) {
        // updateUserDto.password = md5(updateUserDto.password);
        updateUserDto.password = updateUserDto.password;
      } else {
        throw new HttpException('密码长度不能大于20位', HttpStatus.BAD_REQUEST);
      }
    } else {
      delete updateUserDto.password;
    }

    updateUserDto.updatedAt = new Date();
    if (typeof updateUserDto.active === 'string')
      updateUserDto.active = +updateUserDto.active;
    await this.userRepository.update(id, updateUserDto);
  }

  // 校验用户角色
  async checkRole(currentUser: any, role: string): Promise<boolean> {
    const currentUserRole = currentUser.role;
    if (currentUserRole === 'superAdmin') return true;
    if (currentUserRole === 'admin' && role !== 'superAdmin') return true;
    return false;
  }

  // 删除用户旧头像
  async removeAvatar(avatar: string) {
    if (!avatar) return;
    const oldAvatarPath = path.join('./public', avatar);
    const exists = fs.existsSync(oldAvatarPath);
    if (exists) {
      await fs.promises.unlink(oldAvatarPath);
    } else {
      console.log(oldAvatarPath + '文件不存在');
    }
  }
}
