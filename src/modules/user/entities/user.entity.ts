import {
  Entity,
  Column,
  Unique,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
@Entity('user')
export class User {
  @PrimaryGeneratedColumn({ comment: '自增ID' })
  id: number;

  @Unique(['userName', 'phoneNum'])
  @Column({ comment: '登录账号', type: 'varchar', length: 32 })
  userName: string;

  @Column({ comment: '用户密码' })
  password?: string;

  @Column({ comment: '姓名', type: 'varchar', length: 32 })
  name: string;

  @Column({ comment: '用户手机', type: 'varchar', length: 11 })
  phoneNum: string;

  @Column({
    comment: '用户角色',
    type: 'enum',
    enum: [
      'superAdmin', // 超级管理员
      'admin', // 管理员
      'user', // 一般用户
    ],
    default: 'user',
  })
  role: string;

  @Column({ comment: '机构名称', type: 'varchar', length: 32, nullable: true })
  orgname?: string;

  @Column({
    comment: '是否启用, 1: 启用 0: 未启用',
    type: 'enum',
    enum: [1, 0],
    default: 1,
  })
  active: number;

  // @Column({ comment: '头像地址' })
  // avatar?: string;

  @Column({
    type: 'varchar',
    comment: 'superUser.members._id',
    length: 32
  })
  openId: string;

  @CreateDateColumn({ comment: '创建时间', type: 'datetime' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间', type: 'datetime' })
  updatedAt: Date;
}
