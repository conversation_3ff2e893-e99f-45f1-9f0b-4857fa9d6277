import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './modules/user/user.module';
import { AuthModule } from './modules/auth/auth.module';
import { LoggerModule } from './modules/logger/logger.module';
import { ToolModule } from './modules/tool/tool.module'

import { EmployeeModule } from './modules/employee/employee.module';
import { EnterpriseModule } from './modules/enterprise/enterprise.module';
import { EmployeeCategoryModule } from './modules/employee-category/employee-category.module';
import { ServiceOrgModule } from './modules/serviceOrg/serviceOrg.module';

import { InfoSetModule } from './modules/employee-info-set/employee-info-set.module';

import { ConfigModule } from '@nestjs/config';
import { ConfigService } from '@nestjs/config';
import defaultConfig from './config/default';
import developmentConfig from './config/development';
import productionConfig from './config/production';

// 整个应用的注册中心
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [
        defaultConfig,
        process.env.NODE_ENV === 'production'
          ? productionConfig
          : developmentConfig,
      ],
    }),
    // 数据库模块 mysql
    TypeOrmModule.forRootAsync({
      name: 'mysqlConnection',
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('database_mysql');
        dbConfig.entities = [__dirname + '/**/entities/*.entity{.ts,.js}']
        return dbConfig;
      },
    }),
    TypeOrmModule.forRootAsync({
      name: 'mongodbConnection',
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('database_mongodb');
        dbConfig.entities = [__dirname + '/**/mongo/*.entity{.ts,.js}']
        return dbConfig;
      },
    }),
    UserModule,
    AuthModule,
    LoggerModule,
    ToolModule,
    EmployeeModule,
    EnterpriseModule,
    EmployeeCategoryModule,
    ServiceOrgModule,

    InfoSetModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
