// 本地上传文件 存储引擎
import * as multer from 'multer';

export const storage = multer.diskStorage({
  destination: './public/uploads',
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const fileType = file.mimetype.split('/')[1];
    cb(null, file.fieldname + '-' + uniqueSuffix + '.' + fileType);
  },
});

// export function createStorage(destination = './public/uploads') {
//   return multer.diskStorage({
//     destination,
//     filename: (req, file, cb) => {
//       const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
//       const fileType = file.mimetype.split('/')[1];
//       cb(null, file.fieldname + '-' + uniqueSuffix + '.' + fileType);
//     },
//   });
// }
