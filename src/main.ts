import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SystemService } from './modules/logger/system/system.service';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'; // 自动生成API文档
import { join } from 'path';
import { NestExpressApplication } from '@nestjs/platform-express'; // 配合useStaticAssets使用

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    cors: true,
    logger: new SystemService(), // 系统日志服务
  });
  app.useStaticAssets(join(__dirname, '..', 'public')); // 配置静态资源目录

  const options = new DocumentBuilder()
    .setTitle('API 文档')
    .setDescription('API 描述')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('api', app, document);

  const configService = app.get(ConfigService);
  const port = configService.get<number>('port');
  await app.listen(port);
}
bootstrap();
